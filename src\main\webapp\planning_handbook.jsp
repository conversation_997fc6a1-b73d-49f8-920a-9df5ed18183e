<%@ page contentType="text/html;charset=UTF-8" language="java"  pageEncoding="UTF-8"%>

<%@ page import="com.daxue.utils.*,com.daxue.db.*,java.util.*"%>

<%
DxAgent dxAgent = (DxAgent)session.getAttribute(DxCache.SES_KEY_T_DX_AGENT);

// 获取学生ID参数并查询学生信息
String studentId = Tools.trim(request.getParameter("id"));

DxCard studentCard = null;
ZyzdUniversityBean universityInfo = null;
List<ZyzdMajorRanking> universityRankings = null;
List<JiuyeBean> universityEmployment = null;
List<ZyzdMajorRanking> majorRankings = null;
List<JiuyeBean> majorEmployment = null;
List<ZyzdBaseUniversityAndMajorTag> universityMajorTags = null;
List<ZyzdBaseUniversityTsb> universityTsb = null;
List<ZyzdBaseUniversityZzy> universityZzy = null;
List<ZyzdBaseUniversityLabs> universityLabs = null;

// 获取当前日期
String currentDate = Tools.getDateYMD(new java.util.Date());

// 性能优化：使用缓存和批量查询
java.util.Map<String, Object> dataCache = new java.util.HashMap<>();

%>

<%

if (studentId != null && !studentId.trim().isEmpty()) {
    try {
        DaxueJdbc daxueJdbc = new DaxueJdbc();
        studentCard = daxueJdbc.getDxCardById(studentId);

        if (studentCard != null) {
            // 优化：批量查询减少数据库访问
            String universityName = studentCard.getC_university();
            String majorName = studentCard.getC_major();

            // 根据学校名称查询学校信息
            if (universityName != null && !universityName.trim().isEmpty()) {
                universityInfo = DxCache.getUniversity(universityName);

                // 并行查询提升性能
                java.util.concurrent.CompletableFuture<java.util.List<ZyzdMajorRanking>> universityRankingsFuture =
                    java.util.concurrent.CompletableFuture.supplyAsync(() -> daxueJdbc.getRkRankingYx(universityName));
                java.util.concurrent.CompletableFuture<java.util.List<JiuyeBean>> universityEmploymentFuture =
                    java.util.concurrent.CompletableFuture.supplyAsync(() -> daxueJdbc.getJiuyeByYx(universityName));

                universityMajorTags = DxCache.getUniversityMajorTags(universityName);

                // 查询特殊班和转专业信息（使用缓存）
                universityTsb = DxCache.getUniversityTsb(universityName);
                universityZzy = DxCache.getUniversityZzy(universityName);

                // 查询国家重点实验室信息（使用缓存）
                universityLabs = DxCache.getUniversityLabs(universityName);

                // 等待异步查询完成
                universityRankings = universityRankingsFuture.get();
                universityEmployment = universityEmploymentFuture.get();
            }

            // 根据专业名称查询专业信息
            if (majorName != null && !majorName.trim().isEmpty()) {
                java.util.concurrent.CompletableFuture<java.util.List<ZyzdMajorRanking>> majorRankingsFuture =
                    java.util.concurrent.CompletableFuture.supplyAsync(() -> daxueJdbc.getRkRankinZy(majorName));
                java.util.concurrent.CompletableFuture<java.util.List<JiuyeBean>> majorEmploymentFuture =
                    java.util.concurrent.CompletableFuture.supplyAsync(() -> daxueJdbc.getJiuyeByZy(majorName));

                majorRankings = majorRankingsFuture.get();
                majorEmployment = majorEmploymentFuture.get();
            }
        }
    } catch (Exception e) {
        Tools.println("查询学生数据时发生错误: " + e.getMessage());
        // 记录错误但不中断页面渲染
    }
}
%>


<%!
// 定义就业人数等级转换方法
String getEmploymentLevel(int count) {
    if (count > 1000) return "A";
    else if (count > 100) return "B";
    else if (count > 10) return "C";
    else return "D";
}

// 定义等级颜色和样式
String getEmploymentLevelStyle(String level) {
    String style = "padding: 1mm 3mm; border-radius: 10pt; font-size: 9pt; font-weight: 700; ";
    switch (level) {
        case "A": return style + "background: #10b981; color: white;";
        case "B": return style + "background: #3b82f6; color: white;";
        case "C": return style + "background: #f59e0b; color: white;";
        case "D": return style + "background: #6b7280; color: white;";
        default: return style + "background: #e5e7eb; color: #374151;";
    }
}

// 数据验证方法
boolean isValidStudentId(String studentId) {
    return studentId != null && studentId.matches("^[a-zA-Z0-9]{1,20}$");
}

// 安全的数据显示方法（使用Tools类中的方法）
String safeDisplay(String value) {
    return Tools.safeDisplay(value);
}

// 格式化转专业要求内容，按目录结构排版
String formatZzyContent(String content) {
    if (content == null || content.trim().isEmpty()) {
        return content;
    }

    // 替换常见的目录结构标识符，提升可读性
    content = content.trim();

    // 处理时间、要求等关键词
    content = content.replaceAll("(时间：|要求：|条件：|规定：|说明：|注意：)", "\n\n$1");

    // 处理中文数字编号（一、二、三、等）- 一级标题
    content = content.replaceAll("([一二三四五六七八九十]+)、", "\n\n$1、");

    // 处理数字编号（1、2、3、等）- 二级标题，添加缩进
    content = content.replaceAll("([0-9]+)、", "\n  $1、");

    // 处理括号编号（(1)、(2)、等）- 三级标题，更多缩进
    content = content.replaceAll("\\(([0-9]+)\\)", "\n    ($1)");

    // 处理字母编号（a、b、c、等）- 四级标题
    content = content.replaceAll("([a-zA-Z])、", "\n      $1、");

    // 处理分号分隔的条款
    content = content.replaceAll("；\\s*([0-9一二三四五六七八九十]+[、.])", "；\n$1");

    // 处理句号后的新条款
    content = content.replaceAll("。\\s*([0-9一二三四五六七八九十]+[、.])", "。\n$1");

    // 处理特殊情况：连续的中文数字编号
    content = content.replaceAll("([一二三四五六七八九十]+)、([^\\n]*?)([一二三四五六七八九十]+)、", "$1、$2\n\n$3、");

    // 清理多余的换行符，但保留段落间距
    content = content.replaceAll("\n{4,}", "\n\n\n");
    content = content.replaceAll("^\n+", "");

    return content;
}
%>

<!-- 当作为独立页面访问时显示完整HTML结构 -->
<%
String isIncluded = request.getParameter("included");
boolean isStandalone = (isIncluded == null);
if (isStandalone) {
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大学入学规划手册</title>
    <!-- 2. Font Awesome → 同步官方 6.4.0） -->
    <link rel="stylesheet" href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css">
    <!-- ECharts CDN -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<%} %>

    <style>
        /* 为planning_handbook.jsp添加命名空间前缀 */
        .planning-handbook-container {
            font-family: 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif;
            font-size: 12pt;
            line-height: 1.6;
            color: #000;
            background: white;
            margin: 0;
            padding: 20px;
            max-width: 250mm;
            margin: 0 auto;
        }

        .planning-handbook-container @page {
            size: A4 portrait;
            margin: 25mm 20mm 25mm 35mm;
        }

        .planning-handbook-container * {
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            box-sizing: border-box;
        }

        .planning-handbook-container .page-section {
            width: 100%;
            min-height: 250mm;
            margin-bottom: 30px;
            padding: 20mm 15mm 20mm 15mm;
            background: white;
            border: 1px solid #ddd;
            /* page-break-after controlled by main planning.jsp */
            position: relative;
            break-inside: avoid;
        }

        /* Last page section styling controlled by main planning.jsp */

        /* 工具栏 - 仅在屏幕显示，打印时隐藏 */
        @media screen {
            .planning-handbook-container .toolbar {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                display: flex;
                gap: 10px;
            }

            .planning-handbook-container .toolbar button {
                padding: 10px 20px;
                background: linear-gradient(135deg, #3b82f6, #1e40af);
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .planning-handbook-container .toolbar button:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
            }

            /* 为屏幕显示添加渐变背景和装饰 */
            .planning-handbook-container {
                background: linear-gradient(135deg, #f0f4f8 0%, #e4ecfb 100%);
            }

            .planning-handbook-container .page-section {
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                border-radius: 12px;
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                border: none;
                position: relative;
                overflow: hidden;
            }

            .planning-handbook-container .page-section::before {
                content: '';
                position: absolute;
                top: -50%;
                right: -50%;
                width: 200%;
                height: 200%;
                background: radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
                pointer-events: none;
            }
        }

        /* Print styles removed - now controlled by main planning.jsp */

        /* 标题样式系统 */
        .planning-handbook-container .title-main {
            font-size: 28pt;
            font-weight: 700;
            color: #1a56db;
            text-align: center;
            margin-bottom: 15mm;
            line-height: 1.2;
            text-shadow: 2px 2px 4px rgba(26, 86, 219, 0.1);
        }

        .planning-handbook-container .title-sub {
            font-size: 24pt;
            font-weight: 700;
            color: #1a56db;
            text-align: center;
            margin-bottom: 10mm;
            text-shadow: 1px 1px 3px rgba(26, 86, 219, 0.1);
        }

        .planning-handbook-container .title-section {
            font-size: 20pt;
            font-weight: 700;
            color: #1a56db;
            margin: 10mm 0 6mm 0;
            border-bottom: 3pt solid #1a56db;
            padding-bottom: 3mm;
            position: relative;
        }

        .planning-handbook-container .title-section::after {
            content: '';
            position: absolute;
            bottom: -3pt;
            left: 0;
            width: 40mm;
            height: 2pt;
            background: #f97316;
        }

        .planning-handbook-container .subtitle {
            font-size: 16pt;
            font-weight: 500;
            color: #f97316;
            text-align: center;
            margin-bottom: 8mm;
        }

        .planning-handbook-container .date {
            font-size: 12pt;
            color: #4b5563;
            text-align: center;
            margin-top: 20mm;
            font-style: italic;
        }

        /* 改进的图标样式 */
        .planning-handbook-container .icon-section {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 30px;
            margin: 20mm 0;
            flex-wrap: wrap;
        }

        .planning-handbook-container .icon-item {
            text-align: center;
            max-width: 120px;
            padding: 8mm;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-radius: 12pt;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        @media screen {
            .planning-handbook-container .icon-item:hover {
                transform: translateY(-4px);
            }
        }

        .planning-handbook-container .icon-item i {
            font-size: 30pt;
            margin-bottom: 4mm;
            display: block;
        }

        .planning-handbook-container .icon-item .blue { color: #3b82f6; }
        .planning-handbook-container .icon-item .red { color: #ef4444; }
        .planning-handbook-container .icon-item .green { color: #10b981; }
        .planning-handbook-container .icon-item .purple { color: #8b5cf6; }

        .planning-handbook-container .icon-item p {
            font-size: 11pt;
            color: #374151;
            margin: 0;
            font-weight: 500;
        }

        /* 精美的目录样式 - 单列竖向排列 */
        .planning-handbook-container .toc-container {
            display: flex;
            flex-direction: column;
            gap: 4mm;
            margin: 8mm 0;
            max-width: 100%;
        }

        .planning-handbook-container .toc-section {
            border: 2pt solid #e5e7eb;
            border-radius: 10pt;
            overflow: hidden;
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            width: 100%;
        }

        .planning-handbook-container .toc-header {
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            padding: 5mm;
            color: white;
        }

        .planning-handbook-container .toc-header h3 {
            margin: 0;
            font-size: 13pt;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 2mm;
        }

        .planning-handbook-container .toc-content {
            padding: 4mm;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 3mm;
        }

        .planning-handbook-container .toc-item {
            display: flex;
            align-items: center;
            padding: 2.5mm;
            border: 1pt solid #e5e7eb;
            border-radius: 4pt;
            background: linear-gradient(135deg, #f8fafc, #ffffff);
            transition: all 0.3s ease;
            position: relative;
            min-height: 12mm;
            cursor: pointer;
        }

        .planning-handbook-container .toc-item a {
            color: inherit;
            text-decoration: none;
            display: flex;
            align-items: center;
            width: 100%;
        }

        @media screen {
            .planning-handbook-container .toc-item:hover {
                transform: translateX(2mm);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
                border-color: #3b82f6;
            }
        }

        .planning-handbook-container .toc-number {
            width: 8mm;
            height: 8mm;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 8pt;
            margin-right: 3mm;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .planning-handbook-container .toc-text {
            font-size: 9pt;
            font-weight: 500;
            color: #374151;
            line-height: 1.3;
            flex: 1;
        }

        /* 卡片网格布局系统 */
        .planning-handbook-container .card-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6mm;
            margin: 10mm 0;
        }

        .planning-handbook-container .card-grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 5mm;
            margin: 10mm 0;
        }

        .planning-handbook-container .card {
            border: 1pt solid #d1d5db;
            border-radius: 8pt;
            padding: 6mm;
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            border-left: 4pt solid #3b82f6;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            break-inside: avoid;
        }

        @media screen {
            .planning-handbook-container .card:hover {
                transform: translateY(-2mm);
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
                border-left-color: #f97316;
            }
        }

        .planning-handbook-container .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 4mm;
        }

        .planning-handbook-container .card-icon {
            width: 12mm;
            height: 12mm;
            border-radius: 50%;
            background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 4mm;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
        }

        .planning-handbook-container .card-title {
            font-size: 14pt;
            font-weight: 700;
            color: #1e40af;
            margin: 0;
        }

        .planning-handbook-container .card ul {
            margin: 0;
            padding-left: 4mm;
        }

        .planning-handbook-container .card li {
            font-size: 11pt;
            color: #374151;
            margin-bottom: 2mm;
            line-height: 1.5;
        }

        /* 时间线样式 */
        .planning-handbook-container .timeline {
            margin: 15mm 0;
            position: relative;
        }

        .planning-handbook-container .timeline-item {
            display: flex;
            margin-bottom: 8mm;
            border-left: 3pt solid #3b82f6;
            padding-left: 8mm;
            position: relative;
            background: linear-gradient(135deg, #f8fafc, #ffffff);
            border-radius: 8pt;
            padding: 6mm 6mm 6mm 10mm;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .planning-handbook-container .timeline-item::before {
            content: '';
            position: absolute;
            left: -6pt;
            top: 8mm;
            width: 10pt;
            height: 10pt;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .planning-handbook-container .timeline-content {
            flex-grow: 1;
        }

        .planning-handbook-container .timeline-date {
            font-size: 12pt;
            font-weight: 700;
            color: #1e40af;
            margin-bottom: 2mm;
            display: flex;
            align-items: center;
            gap: 2mm;
        }

        .planning-handbook-container .timeline-title {
            font-size: 13pt;
            font-weight: 600;
            color: #374151;
            margin-bottom: 2mm;
        }

        .planning-handbook-container .timeline-description {
            font-size: 11pt;
            color: #6b7280;
            line-height: 1.6;
        }

        /* 信息提示框 */
        .planning-handbook-container .info-box {
            border: 1pt solid #3b82f6;
            border-left: 4pt solid #3b82f6;
            background: linear-gradient(135deg, #eff6ff, #dbeafe);
            padding: 6mm;
            margin: 8mm 0;
            border-radius: 6pt;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
        }

        .planning-handbook-container .info-box-title {
            font-size: 13pt;
            font-weight: 700;
            color: #1e40af;
            margin-bottom: 3mm;
            display: flex;
            align-items: center;
            gap: 2mm;
        }

        .planning-handbook-container .info-box-content {
            font-size: 11pt;
            color: #1e40af;
            line-height: 1.6;
        }

        /* 标签样式 */
        .tag {
            display: inline-block;
            padding: 1mm 3mm;
            background: linear-gradient(135deg, #eff6ff, #dbeafe);
            color: #1e40af;
            border-radius: 10pt;
            font-size: 9pt;
            font-weight: 500;
            margin-right: 2mm;
            margin-bottom: 2mm;
            border: 1pt solid #bfdbfe;
        }

        /* 页眉页脚 */
        .planning-handbook-container .page-header {
            text-align: center;
            font-size: 11pt;
            color: #6b7280;
            border-bottom: 1pt solid #e5e7eb;
            padding-bottom: 3mm;
            margin-bottom: 8mm;
            font-weight: 500;
        }

        .planning-handbook-container .page-footer {
            position: absolute;
            bottom: 10mm;
            left: 15mm;
            right: 15mm;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 10pt;
            color: #6b7280;
            border-top: 1pt solid #e5e7eb;
            padding-top: 3mm;
        }

        /* Page footer styles - print formatting controlled by main planning.jsp */

        /* 响应式设计优化 */
        @media screen and (max-width: 1200px) {
            .planning-handbook-container .card-grid-3 {
                grid-template-columns: 1fr 1fr;
            }

            .planning-handbook-container .toc-content {
                grid-template-columns: 1fr 1fr 1fr;
            }
        }

        @media screen and (max-width: 768px) {
            .planning-handbook-container {
                padding: 10px;
                font-size: 11pt;
            }

            .planning-handbook-container .card-grid {
                grid-template-columns: 1fr;
                gap: 4mm;
            }

            .planning-handbook-container .toc-content {
                grid-template-columns: 1fr 1fr;
                gap: 3mm;
            }

            .planning-handbook-container .card-grid-3 {
                grid-template-columns: 1fr;
            }

            .planning-handbook-container .icon-section {
                gap: 15px;
                flex-direction: column;
            }

            .planning-handbook-container .page-section {
                padding: 10mm 8mm;
                min-height: auto;
            }

            .planning-handbook-container .title-main {
                font-size: 22pt;
            }

            .planning-handbook-container .title-section {
                font-size: 16pt;
            }

            .planning-handbook-container .card {
                padding: 4mm;
            }

            .planning-handbook-container .timeline-item {
                padding: 4mm 4mm 4mm 8mm;
            }
        }

        @media screen and (max-width: 480px) {
            .planning-handbook-container {
                font-size: 10pt;
            }

            .planning-handbook-container .title-main {
                font-size: 18pt;
            }

            .planning-handbook-container .card-header {
                flex-direction: column;
                text-align: center;
            }

            .planning-handbook-container .card-icon {
                margin-right: 0;
                margin-bottom: 2mm;
            }

            .planning-handbook-container .toolbar {
                position: relative;
                top: auto;
                right: auto;
                margin-bottom: 10px;
                justify-content: center;
            }
        }

        /* 中等屏幕调整 - 确保目录在打印时合理显示 */
        @media screen and (max-width: 1024px) {
            .planning-handbook-container .toc-content {
                grid-template-columns: 1fr 1fr;
                gap: 3mm;
            }
        }

        /* 小屏幕优化 */
        @media screen and (max-width: 600px) {
            .planning-handbook-container .toc-content {
                grid-template-columns: 1fr;
                gap: 2mm;
            }
        }

        /* Table of contents styles - print formatting controlled by main planning.jsp */

        /* Layout and content styles - print formatting controlled by main planning.jsp */
    </style>
<%if (isStandalone) {%>
</head>
<body>
<%}%>
<div class="planning-handbook-container">
    <!-- 工具栏 -->
<!--     <div class="toolbar">
        <button onclick="window.print()">
            <i class="fas fa-print"></i>
            打印手册
        </button>
    </div> -->

    <!-- Section 1: 封面页 -->
    <section class="page-section">
        <div class="page-main-content">
            <div style="text-align: center; margin-top: 20mm;">
                <i class="fas fa-university" style="font-size: 40pt; color: #3b82f6; margin-bottom: 10mm;"></i>
                <h1 class="title-main">大学入学规划手册</h1>
                <div style="width: 60mm; height: 3pt; background: linear-gradient(90deg, #f97316, #fb923c); margin: 0 auto 15mm; border-radius: 2pt;"></div>
                <h2 class="subtitle">从入学准备到毕业规划的完整指南</h2>

                <%if (studentCard != null) { %>
                <!-- 学生基本信息 -->
                <div style="margin: 20mm 0; padding: 8mm; background: linear-gradient(135deg, #f8fafc, #f1f5f9); border: 2pt solid #e2e8f0; border-radius: 8pt;">
                    <table style="width: 100%; border-collapse: collapse; font-size: 12pt;">
                        <tr>
                            <td style="padding: 3mm; color: #64748b; width: 20%; border-bottom: 1pt solid #e2e8f0;">姓名</td>
                            <td style="padding: 3mm; font-weight: 700; color: #0f172a; width: 30%; border-bottom: 1pt solid #e2e8f0;"><%=safeDisplay(studentCard.getC_name())%></td>
                            <td style="padding: 3mm; color: #64748b; width: 20%; border-bottom: 1pt solid #e2e8f0;">省份</td>
                            <td style="padding: 3mm; font-weight: 700; color: #0f172a; width: 30%; border-bottom: 1pt solid #e2e8f0;"><%=safeDisplay(studentCard.getC_prov())%></td>
                        </tr>
                        <tr>
                            <td style="padding: 3mm; color: #64748b; border-bottom: 1pt solid #e2e8f0;">城市</td>
                            <td style="padding: 3mm; font-weight: 700; color: #0f172a; border-bottom: 1pt solid #e2e8f0;"><%=safeDisplay(studentCard.getC_city())%></td>
                            <td style="padding: 3mm; color: #64748b; border-bottom: 1pt solid #e2e8f0;">学校</td>
                            <td style="padding: 3mm; font-weight: 700; color: #0f172a; border-bottom: 1pt solid #e2e8f0;"><%=safeDisplay(studentCard.getC_university())%></td>
                        </tr>
                        <tr>
                            <td style="padding: 3mm; color: #64748b; border-bottom: 1pt solid #e2e8f0;">专业</td>
                            <td style="padding: 3mm; font-weight: 700; color: #0f172a; border-bottom: 1pt solid #e2e8f0;"><%=safeDisplay(studentCard.getC_major())%></td>
                            <td style="padding: 3mm; color: #64748b; border-bottom: 1pt solid #e2e8f0;">创建时间</td>
                            <td style="padding: 3mm; font-weight: 700; color: #0f172a; border-bottom: 1pt solid #e2e8f0;"><%=studentCard.getCreate_tm() != null ? new java.text.SimpleDateFormat("yyyy年M月d日").format(studentCard.getCreate_tm()) : currentDate%></td>
                        </tr>
                    </table>
                </div>
                <%} %>

			<%-- <div class="date"><%=currentDate%></div> --%>
            </div>
        </div>

        <div class="page-footer">
            <span><i class="fas fa-book"></i> 入学准备指南</span>
            <span>第 1 页</span>
        </div>
    </section>

    <!-- Section 2: 目录 -->
    <section class="page-section">
        <div class="page-main-content">
            <h2 class="title-sub">目录</h2>
            <div style="width: 40mm; height: 3pt; background: linear-gradient(90deg, #f97316, #fb923c); margin-bottom: 10mm; border-radius: 2pt;"></div>

            <div class="toc-container">
                <div class="toc-section">
                    <div class="toc-header">
                        <h3><i class="fas fa-info-circle"></i> 学校专业信息</h3>
                    </div>
                    <div class="toc-content">
                        <div class="toc-item">
                            <a href="#section-university-info">
                                <div class="toc-number">01</div>
                                <div class="toc-text">院校基本信息</div>
                            </a>
                        </div>
                        <div class="toc-item">
                            <a href="#section-major-ranking">
                                <div class="toc-number">02</div>
                                <div class="toc-text">专业排名情况</div>
                            </a>
                        </div>
                        <div class="toc-item">
                            <a href="#section-career-positions">
                                <div class="toc-number">03</div>
                                <div class="toc-text">国考省考岗位</div>
                            </a>
                        </div>
                        <div class="toc-item">
                            <a href="#section-graduate-admission">
                                <div class="toc-number">04</div>
                                <div class="toc-text">研究生招生计划</div>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="toc-section">
                    <div class="toc-header">
                        <h3><i class="fas fa-university"></i> 入学准备</h3>
                    </div>
                    <div class="toc-content">
                        <div class="toc-item">
                            <a href="#section-admission-materials">
                                <div class="toc-number">05</div>
                                <div class="toc-text">入学材料准备</div>
                            </a>
                        </div>
                        <div class="toc-item">
                            <a href="#section-living-supplies">
                                <div class="toc-number">06</div>
                                <div class="toc-text">生活用品准备</div>
                            </a>
                        </div>
                        <div class="toc-item">
                            <a href="#section-mental-preparation">
                                <div class="toc-number">07</div>
                                <div class="toc-text">心理准备</div>
                            </a>
                        </div>
                        <div class="toc-item">
                            <a href="#section-study-preparation">
                                <div class="toc-number">08</div>
                                <div class="toc-text">学习准备</div>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="toc-section">
                    <div class="toc-header">
                        <h3><i class="fas fa-calendar-alt"></i> 四年规划</h3>
                    </div>
                    <div class="toc-content">
                        <div class="toc-item">
                            <a href="#section-major-requirements">
                                <div class="toc-number">09</div>
                                <div class="toc-text">专业准备要求</div>
                            </a>
                        </div>
                        <div class="toc-item">
                            <a href="#section-enrollment-procedures">
                                <div class="toc-number">10</div>
                                <div class="toc-text">入学手续办理</div>
                            </a>
                        </div>
                        <div class="toc-item">
                            <a href="#section-faq">
                                <div class="toc-number">11</div>
                                <div class="toc-text">常见问题解答</div>
                            </a>
                        </div>
                        <div class="toc-item">
                            <a href="#section-four-year-plan">
                                <div class="toc-number">12</div>
                                <div class="toc-text">四年规划指南</div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="page-footer">
            <span><i class="fas fa-list"></i> 目录导航</span>
            <span>第 2 页</span>
        </div>
    </section>

    <%if (studentCard != null) { %>
    <!-- 学校和专业详细信息 -->
    <section class="page-section" id="section-university-info">
        <div class="page-main-content">
            <h2 class="title-section">
                <i class="fas fa-university" style="margin-right: 3mm;"></i>院校专业详细信息
            </h2>
            
            <%if (universityInfo != null) { %>
            <!-- 学校详细信息 -->
            <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
	            <i class="fas fa-university"></i><%=Tools.view(studentCard.getC_university())%> 
            </h3>
            
            <div class="card-grid">
                <!-- 基本信息 -->
                <div class="card" style="border-left-color: #3b82f6;">
                    <div class="card-header">
                        <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <h3 class="card-title" style="color: #1e40af;">基本信息</h3>
                    </div>
                    <ul>
                        <li><strong>学校名称：</strong><%=Tools.view(universityInfo.getYxmc())%></li>
                        <li><strong>学校类型：</strong><%=Tools.view(universityInfo.getInd_type())%></li>
                        <li><strong>学校性质：</strong><%=Tools.view(universityInfo.getInd_nature())%></li>
                        <li><strong>隶属于：</strong><%=Tools.view(universityInfo.getLsy())%></li>
                        <li><strong>成立时间：</strong><%=Tools.view(universityInfo.getInfo_found_time())%></li>
                        <li><strong>学校地址：</strong><%=Tools.view(universityInfo.getPosition_sf())%><%=Tools.view(universityInfo.getPosition_cs())%><%=Tools.view(universityInfo.getPosition_qy())%></li>
                        <li><strong>官方网站：</strong><%=Tools.view(universityInfo.getInfo_website())%></li>
                        <li><strong>联系电话：</strong><%=Tools.view(universityInfo.getInfo_tel())%></li>
                    </ul>
                </div>

                
                <!-- 学科建设 -->
                <div class="card" style="border-left-color: #f59e0b;">
                    <div class="card-header">
                        <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h3 class="card-title" style="color: #92400e;">学科建设</h3>
                    </div>
                    <ul>
                        <li><strong>硕士点数量：</strong><%=Tools.view(universityInfo.getCnt_master())%></li>
                        <li><strong>博士点数量：</strong><%=Tools.view(universityInfo.getCnt_phd())%></li>
                        <li><strong>重点实验室：</strong><%=Tools.view(universityInfo.getCnt_laboratory())%></li>
                        <%
                        // 获取保研率信息
                        String baoyanRate = DxCache.getUniversityBaoyanRate(studentCard.getC_university());
                        if (baoyanRate != null && !baoyanRate.trim().isEmpty()) {
                        %>
                        <li><strong>保研率：</strong><span style="color: #059669; font-weight: 600;"><%=baoyanRate%></span></li>
                        <%} %>


                        <%
                        // 显示优势学科信息（从专业标签数据中获取硕博士专业信息）
                        if (universityMajorTags != null && !universityMajorTags.isEmpty()) {
                            // 获取全校硕士专业数和博士专业数
                            String masterCount = null;
                            String doctorCount = null;
                            for (ZyzdBaseUniversityAndMajorTag tag : universityMajorTags) {
                                if (tag.getQxsszys() != null && !tag.getQxsszys().trim().isEmpty()) {
                                    masterCount = tag.getQxsszys();
                                    break;
                                }
                            }
                            for (ZyzdBaseUniversityAndMajorTag tag : universityMajorTags) {
                                if (tag.getQxbszys() != null && !tag.getQxbszys().trim().isEmpty()) {
                                    doctorCount = tag.getQxbszys();
                                    break;
                                }
                            }

                            if (masterCount != null && !masterCount.trim().isEmpty()) {
                        %>
                        <li><strong>全校硕士专业数：</strong><span style="color: #3b82f6; font-weight: 600;"><%=masterCount%></span></li>
                        <%  }
                            if (doctorCount != null && !doctorCount.trim().isEmpty()) {
                        %>
                        <li><strong>全校博士专业数：</strong><span style="color: #8b5cf6; font-weight: 600;"><%=doctorCount%></span></li>
                        <%  }
                        } %>
                        
                                                <%
                        // 获取院校标签信息
                        String universityTags = DxCache.getUniversityTags(studentCard.getC_university());
                        if (universityTags != null && !universityTags.trim().isEmpty()) {
                        %>
                        <li><strong>院校特色：</strong>
                            <%
                            String[] tagArray = universityTags.split(",|，|;|；");
                            for (String tag : tagArray) {
                                if (!tag.trim().isEmpty()) {
                            %>
                            <span class="tag" style="background: #fef3c7; color: #92400e; margin-right: 2mm;"><%=tag.trim()%></span>
                            <%  }
                            } %>
                        </li>
                        <%} %>
                    </ul>
                </div>
                
                <!-- 排名信息 -->
                <div class="card" style="border-left-color: #8b5cf6;">
                    <div class="card-header">
                        <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <h3 class="card-title" style="color: #581c87;">排名信息</h3>
                    </div>
                    <ul>
                        <li><strong>QS排名：</strong><%=Tools.view(universityInfo.getRanking_qs())%></li>
                        <li><strong>软科排名：</strong><%=Tools.view(universityInfo.getRanking_rk())%></li>
                        <li><strong>US News排名：</strong><%=Tools.view(universityInfo.getRanking_usnews())%></li>
                        <li><strong>泰晤士排名：</strong><%=Tools.view(universityInfo.getRanking_wsl())%></li>
                        <li><strong>校友会排名：</strong><%=Tools.view(universityInfo.getRanking_xyh())%></li>
                    </ul>
                </div>
                
                <!-- 就业信息 -->
                <div class="card" style="border-left-color: #ef4444;">
                    <div class="card-header">
                        <div class="card-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white;">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <h3 class="card-title" style="color: #991b1b;">就业信息</h3>
                    </div>
                    <ul>
                       <%--  <li><strong>毕业生数量：</strong><%=universityInfo.getCnt_grad() > 0 ? String.format("%.0f人", universityInfo.getCnt_grad()) : "-"%></li> --%>
                        <li><strong>就业公司数量：</strong><%=universityInfo.getCnt_company() > 0 ? universityInfo.getCnt_company() + "家" : "-"%></li>
                        <li><strong>就业人数：</strong><%=universityInfo.getCnt_employ() > 0 ? universityInfo.getCnt_employ() + "人" : "-"%></li>
                    </ul>
                </div>
                
                <!-- 就业信息图表 - 只有在有数据时才显示 -->
                <%
                // 检查是否有有效的就业数据
                boolean hasEmploymentData = false;
                if (universityInfo != null) {
                    int companyCount = universityInfo.getCnt_company();
                    int employCount = universityInfo.getCnt_employ();
                    // 只有当公司数量或就业人数大于0时才认为有有效数据
                    hasEmploymentData = (companyCount > 0 || employCount > 0);
                }

                if (hasEmploymentData) {
                %>
                <div class="card" id="employmentChartCard" style="border-left-color: #ef4444; grid-column: span 2;">
                    <div class="card-header">
                        <div class="card-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white;">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <h3 class="card-title" style="color: #991b1b;">就业数据可视化</h3>
                    </div>
                    <div id="employmentChart" style="width: 100%; height: 300px; margin-top: 10px;"></div>
                </div>
                <%} %>
                

            </div>
            
            <%if (universityInfo.getInfo_descp() != null && !universityInfo.getInfo_descp().trim().isEmpty()) { %>
            <div class="info-box" style="margin-top: 8mm;">
                <div class="info-box-title"><i class="fas fa-quote-left"></i> 学校简介</div>
                <div class="info-box-content">
                    <%=universityInfo.getInfo_descp()%>... [ 查看详情：
                    <a href="<%=Tools.view(universityInfo.getInfo_website())%>" target="_blank">
                        <%=Tools.view(universityInfo.getInfo_website())%>
                    </a> ]

                </div>
            </div>
            <%} %>
            
            <%} else { %>
<!--             <div class="info-box" style="background: linear-gradient(135deg, #fef3c7, #fed7aa);">
                <div class="info-box-title" style="color: #92400e;"><i class="fas fa-exclamation-triangle"></i> 提示</div>
                <div class="info-box-content" style="color: #92400e;">
                    暂无该院校的详细信息数据。
                </div>
            </div> -->
                         <%} %>
             
             <!-- 院校软科排行 -->
             <%if (universityRankings != null && !universityRankings.isEmpty()) { %>
             <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;" id="section-major-ranking">
                 <i class="fas fa-chart-line" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_university())%> （专业软科排名情况）
             </h3>
             
             <div style="overflow-x: auto; margin: 6mm 0;">
                 <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8pt; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
                     <thead style="background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;">
                         <tr>
                             <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #1e40af; font-size: 14px;">国内排名</th>
                             <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #1e40af; font-size: 14px;">专业名称</th>
                             <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #1e40af; font-size: 14px;">等级</th>
                         </tr>
                     </thead>
                     <tbody>
                         <%for (int i = 0; i < Math.min(20, universityRankings.size()); i++) { 
                             ZyzdMajorRanking ranking = universityRankings.get(i);
                         %>
                         <tr style="<%=i % 2 == 0 ? "background: #f8fafc;" : "background: white;"%>">
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px;"><%=ranking.getRanking() > 0 ? ranking.getRanking() : "-"%></td>
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 500; font-size: 14px;"><%=Tools.view(ranking.getZymc())%></td>
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px;">
                                 <%
                                 String level = ranking.getLevel();
                                 if (level != null && !level.trim().isEmpty()) {
                                     String levelColor = "";
                                     if (level.contains("A+")) levelColor = "background: #10b981; color: white;";
                                     else if (level.contains("A")) levelColor = "background: #3b82f6; color: white;";
                                     else if (level.contains("B+")) levelColor = "background: #f59e0b; color: white;";
                                     else if (level.contains("B")) levelColor = "background: #8b5cf6; color: white;";
                                     else levelColor = "background: #6b7280; color: white;";
                                 %>
                                 <span style="padding: 1mm 3mm; border-radius: 10pt; font-size: 9pt; font-weight: 500; <%=levelColor%>"><%=level%></span>
                                 <%
                                 } else {
                                 %>
                                 <span style="color: #6b7280;">-</span>
                                 <%
                                 }
                                 %>
                             </td>
                         </tr>
                         <%} %>
                     </tbody>
                 </table>
             </div>
             <%} else { %>
<%--              <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                 <i class="fas fa-chart-line" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_university())%> 专业软科排行
             </h3>
             <div class="info-box" style="background: linear-gradient(135deg, #fef3c7, #fed7aa);">
                 <div class="info-box-title" style="color: #92400e;"><i class="fas fa-exclamation-triangle"></i> 提示</div>
                 <div class="info-box-content" style="color: #92400e;">
                     暂无该院校的专业软科排行数据。
                 </div>
             </div> --%>
             <%} %>

             <!-- 学科评估排名 -->
             <%if (universityMajorTags != null && !universityMajorTags.isEmpty()) {
                 // 收集有学科评估数据的专业
                 java.util.Map<String, String> xkpgMap = new java.util.HashMap<>();
                 for (ZyzdBaseUniversityAndMajorTag tag : universityMajorTags) {
                     if (tag.getXkpg() != null && !tag.getXkpg().trim().isEmpty() && !tag.getXkpg().equals("null")) {
                         String majorName = tag.getZymc_org();
                         if (majorName != null && !majorName.trim().isEmpty()) {
                             xkpgMap.put(majorName, tag.getXkpg());
                         }
                     }
                 }

                 if (!xkpgMap.isEmpty()) {
             %>
             <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                 <i class="fas fa-award" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_university())%> （学科评估 第四/五轮）
             </h3>

             <div style="overflow-x: auto; margin: 6mm 0;">
                 <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8pt; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
                     <thead style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;">
                         <tr>
                             <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #7c3aed; font-size: 14px;">专业名称</th>
                             <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #7c3aed; font-size: 14px;">学科评估等级</th>
                         </tr>
                     </thead>
                     <tbody>
                         <%
                         int rowIndex = 0;
                         for (java.util.Map.Entry<String, String> entry : xkpgMap.entrySet()) {
                             String majorName = entry.getKey();
                             String xkpgLevel = entry.getValue();
                         %>
                         <tr style="<%=rowIndex % 2 == 0 ? "background: #f8fafc;" : "background: white;"%>">
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 500; font-size: 14px;"><%=Tools.view(majorName)%></td>
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px;">
                                 <%
                                 if (xkpgLevel != null && !xkpgLevel.trim().isEmpty()) {
                                     String levelColor = "";
                                     if (xkpgLevel.contains("A+")) levelColor = "background: #10b981; color: white;";
                                     else if (xkpgLevel.contains("A-")) levelColor = "background: #059669; color: white;";
                                     else if (xkpgLevel.contains("A")) levelColor = "background: #3b82f6; color: white;";
                                     else if (xkpgLevel.contains("B+")) levelColor = "background: #f59e0b; color: white;";
                                     else if (xkpgLevel.contains("B-")) levelColor = "background: #d97706; color: white;";
                                     else if (xkpgLevel.contains("B")) levelColor = "background: #8b5cf6; color: white;";
                                     else if (xkpgLevel.contains("C+")) levelColor = "background: #ef4444; color: white;";
                                     else if (xkpgLevel.contains("C-")) levelColor = "background: #dc2626; color: white;";
                                     else if (xkpgLevel.contains("C")) levelColor = "background: #f87171; color: white;";
                                     else levelColor = "background: #6b7280; color: white;";
                                 %>
                                 <span style="padding: 1mm 3mm; border-radius: 10pt; font-size: 10pt; font-weight: 600; <%=levelColor%>"><%=xkpgLevel%></span>
                                 <%
                                 } else {
                                 %>
                                 <span style="color: #6b7280;">-</span>
                                 <%
                                 }
                                 %>
                             </td>
                         </tr>
                         <%
                         rowIndex++;
                         } %>
                     </tbody>
                 </table>
             </div>

			<div style="margin-top: 4mm; padding: 4mm; background: #f8fafc; border-radius: 8pt; border-left: 4pt solid #8b5cf6;">
			  <div style="font-size: 11pt; color: #64748b; line-height: 1.6;">
			      <strong style="color: #581c87; font-size: 12pt;">学科评估等级说明：</strong>
			      <div style="margin-top: 3mm; display: flex; flex-wrap: wrap; gap: 2mm; align-items: center;">
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #10b981; color: white; white-space: nowrap;">A+</span> 
			          <span style="color: #374151; margin-right: 4mm;">前 2% 或前 2 名</span>
			          
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #3b82f6; color: white; white-space: nowrap;">A</span> 
			          <span style="color: #374151; margin-right: 4mm;">2%-5%</span>
			          
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #059669; color: white; white-space: nowrap;">A-</span> 
			          <span style="color: #374151; margin-right: 4mm;">5%-10%</span>
			          
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #f59e0b; color: white; white-space: nowrap;">B+</span> 
			          <span style="color: #374151; margin-right: 4mm;">10%-20%</span>
			          
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #8b5cf6; color: white; white-space: nowrap;">B</span> 
			          <span style="color: #374151; margin-right: 4mm;">20%-30%</span>
			      </div>
			      <div style="margin-top: 2mm; display: flex; flex-wrap: wrap; gap: 2mm; align-items: center;">
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #d97706; color: white; white-space: nowrap;">B-</span> 
			          <span style="color: #374151; margin-right: 4mm;">30%-40%</span>
			          
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #ef4444; color: white; white-space: nowrap;">C+</span> 
			          <span style="color: #374151; margin-right: 4mm;">40%-50%</span>
			          
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #f87171; color: white; white-space: nowrap;">C</span> 
			          <span style="color: #374151; margin-right: 4mm;">50%-60%</span>
			          
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #dc2626; color: white; white-space: nowrap;">C-</span> 
			          <span style="color: #374151;">60%-70%</span>
			      </div>
			  </div>
			</div>
             <%  }
             } %>

             <!-- 国家重点实验室信息 -->
             <%if (universityLabs != null && !universityLabs.isEmpty()) { %>
             <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                 <i class="fas fa-flask" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_university())%> 国家重点实验室
             </h3>

             <div class="card-grid">
                 <%
                 int labIndex = 0;
                 String[] labColors = {"#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#06b6d4"};
                 for (ZyzdBaseUniversityLabs lab : universityLabs) {
                     String color = labColors[labIndex % labColors.length];
                     labIndex++;
                 %>
                 <div class="card" style="border-left-color: <%=color%>;">
                     <div class="card-header">
                         <div class="card-icon" style="background: linear-gradient(135deg, <%=color%>, <%=color%>99); color: white;">
                             <i class="fas fa-flask"></i>
                         </div>
                         <h3 class="card-title" style="color: <%=color%>;"><%=Tools.view(lab.getLab_name())%></h3>
                     </div>
                     <ul>
                         <li><strong>依托单位：</strong><%=Tools.view(lab.getSupporting_unit())%></li>
                         <%if (lab.getRegion() != null && !lab.getRegion().trim().isEmpty()) { %>
                         <li><strong>所在地区：</strong><%=Tools.view(lab.getRegion())%></li>
                         <%} %>
                         <%if (lab.getSecondary_college() != null && !lab.getSecondary_college().trim().isEmpty()) { %>
                         <li><strong>依托二级学院：</strong><%=Tools.view(lab.getSecondary_college())%></li>
                         <%} %>
                         <%if (lab.getYear_added() != null) { %>
                         <li><strong>新增年份：</strong><%=lab.getYear_added()%></li>
                         <%} %>
                     </ul>
                 </div>
                 <%} %>
             </div>
             <%} %>

             <!-- 特殊班信息 -->
             <%if (universityTsb != null && !universityTsb.isEmpty()) { %>
             <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                 <i class="fas fa-star" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_university())%> 特色班信息
             </h3>

             <%
             // 按班型分组
             java.util.Map<String, java.util.List<ZyzdBaseUniversityTsb>> tsbGroupMap = new java.util.LinkedHashMap<>();
             for (ZyzdBaseUniversityTsb tsb : universityTsb) {
                 String bx = tsb.getBx();
                 if (bx == null || bx.trim().isEmpty()) {
                     bx = "其他";
                 }
                 if (!tsbGroupMap.containsKey(bx)) {
                     tsbGroupMap.put(bx, new java.util.ArrayList<>());
                 }
                 tsbGroupMap.get(bx).add(tsb);
             }
             %>

             <div class="card-grid">
                 <%
                 int tsbIndex = 0;
                 for (java.util.Map.Entry<String, java.util.List<ZyzdBaseUniversityTsb>> entry : tsbGroupMap.entrySet()) {
                     String bxName = entry.getKey();
                     java.util.List<ZyzdBaseUniversityTsb> tsbList = entry.getValue();
                     String[] colors = {"#3b82f6", "#10b981", "#f59e0b", "#ef4444", "#8b5cf6", "#06b6d4"};
                     String color = colors[tsbIndex % colors.length];
                     tsbIndex++;
                 %>
                 <div class="card" style="border-left-color: <%=color%>;">
                     <div class="card-header">
                         <div class="card-icon" style="background: linear-gradient(135deg, <%=color%>, <%=color%>99); color: white;">
                             <i class="fas fa-graduation-cap"></i>
                         </div>
                         <h3 class="card-title" style="color: <%=color%>;"><%=Tools.view(bxName)%></h3>
                     </div>
                     <ul>
                         <%for (ZyzdBaseUniversityTsb tsb : tsbList) { %>
                         <li>
                             <%if (tsb.getJs() != null && !tsb.getJs().trim().isEmpty()) { %>
                             <strong>介绍：</strong><%=Tools.view(tsb.getJs())%>
                             <%} %>
                             <%if (tsb.getBz() != null && !tsb.getBz().trim().isEmpty()) { %>
                             <br><strong>备注：</strong><%=Tools.view(tsb.getBz())%>
                             <%} %>
                         </li>
                         <%} %>
                     </ul>
                 </div>
                 <%} %>
             </div>
             <%} %>

             <!-- 转专业信息 -->
             <%if (universityZzy != null && !universityZzy.isEmpty()) { %>
             <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                 <i class="fas fa-exchange-alt" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_university())%> 转专业政策
             </h3>

             <div style="overflow-x: auto; margin: 6mm 0;">
                 <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8pt; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
                     <thead style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;">
                         <tr>
                             <th style="padding: 3mm; width: 15%; text-align: center; font-weight: 700; border-bottom: 2pt solid #7c3aed; font-size: 14px;">年份</th>
                             <th style="padding: 3mm; width: 85%; text-align: left; font-weight: 700; border-bottom: 2pt solid #7c3aed; font-size: 14px;">转专业要求</th>
                         </tr>
                     </thead>
                     <tbody>
                         <%for (int i = 0; i < universityZzy.size(); i++) {
                             ZyzdBaseUniversityZzy zzy = universityZzy.get(i);
                             String rowStyle = i % 2 == 0 ? "background: #f8fafc;" : "background: white;";
                         %>
                         <tr style="<%=rowStyle%>">
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; text-align: center; font-weight: 700; color: #7c3aed; font-size: 14px;"><%=zzy.getNf()%></td>
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; line-height: 1.8;">
                                 <%
                                 String yqContent = Tools.view(zzy.getYq());
                                 if (yqContent != null && !yqContent.trim().isEmpty()) {
                                     // 格式化转专业要求内容，按目录结构排版
                                     yqContent = formatZzyContent(yqContent);
                                 %>
                                 <div style="white-space: pre-line; font-family: 'Microsoft YaHei', sans-serif; color: #374151;"><%=yqContent%></div>
                                 <%} else { %>
                                 <span style="color: #9ca3af; font-style: italic;">暂无详细要求</span>
                                 <%} %>
                             </td>
                         </tr>
                         <%} %>
                     </tbody>
                 </table>
             </div>
             <%} %>

             <!-- 院校就业统计 -->
             <%if (universityEmployment != null && !universityEmployment.isEmpty()) { %>
             <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                 <i class="fas fa-briefcase" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_university())%> 就业统计（前20个行业）
             </h3>
             
             <div style="overflow-x: auto; margin: 6mm 0;">
                 <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8pt; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
                     <thead style="background: linear-gradient(135deg, #10b981, #059669); color: white;">
                         <tr>
                             <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #059669; font-size: 14px;">排名</th>
                             <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #059669; font-size: 14px;">行业名称</th>
                             <th style="padding: 3mm; text-align: center; font-weight: 700; border-bottom: 2pt solid #059669; font-size: 14px;">就业等级</th>
                         </tr>
                     </thead>
                     <tbody>
                         <%for (int i = 0; i < universityEmployment.size(); i++) { 
                             JiuyeBean employment = universityEmployment.get(i);
                             String level = getEmploymentLevel(employment.getCnt());
                             String levelStyle = getEmploymentLevelStyle(level);
                             
                         %>
                         <tr style="<%=i % 2 == 0 ? "background: #f0fdf4;" : "background: white;"%>">
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 700; color: #059669; font-size: 14px;"><%=i + 1%></td>
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 500; font-size: 14px;"><%=Tools.view(employment.getSshy())%></td>
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; text-align: center; font-size: 14px;">
                                 <span style="<%=levelStyle%>"><%=level%></span>
                             </td>
                         </tr>
                         <%} %>
                     </tbody>
                 </table>
             </div>


             <!-- 等级说明 -->
             <div class="info-box" style="margin-top: 6mm;">
                 <div class="info-box-title"><i class="fas fa-info-circle"></i> 等级说明</div>
                 <div class="info-box-content">
                     <span style="padding: 1mm 3mm; border-radius: 10pt; font-size: 9pt; font-weight: 700; background: #10b981; color: white; margin-right: 3mm;">A</span> 就业人数 > 1000人
                     <span style="padding: 1mm 3mm; border-radius: 10pt; font-size: 9pt; font-weight: 700; background: #3b82f6; color: white; margin: 0 3mm;">B</span> 就业人数 > 100人
                     <span style="padding: 1mm 3mm; border-radius: 10pt; font-size: 9pt; font-weight: 700; background: #f59e0b; color: white; margin: 0 3mm;">C</span> 就业人数 > 10人
                     <span style="padding: 1mm 3mm; border-radius: 10pt; font-size: 9pt; font-weight: 700; background: #6b7280; color: white; margin-left: 3mm;">D</span> 就业人数 ≤ 10人
                 </div>
             </div>
             <%} else { %>
<%--              <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                 <i class="fas fa-briefcase" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_university())%> 就业统计
             </h3>
             <div class="info-box" style="background: linear-gradient(135deg, #fef3c7, #fed7aa);">
                 <div class="info-box-title" style="color: #92400e;"><i class="fas fa-exclamation-triangle"></i> 提示</div>
                 <div class="info-box-content" style="color: #92400e;">
                     暂无该院校的就业统计数据。
                 </div>
             </div> --%>
             <%} %>
             
             <!-- 专业软科排行 -->
             <%if (majorRankings != null && !majorRankings.isEmpty()) { %>
             <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                 <i class="fas fa-medal" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_major())%> 专业软科排行
             </h3>
             
             <div style="overflow-x: auto; margin: 6mm 0;">
                 <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8pt; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
                     <thead style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">
                         <tr>
                             <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #d97706; font-size: 14px;">国内排名</th>
                             <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #d97706; font-size: 14px;">院校名称</th>
                             <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #d97706; font-size: 14px;">等级</th>
                         </tr>
                     </thead>
                     <tbody>
                         <%for (int i = 0; i < majorRankings.size(); i++) { 
                             ZyzdMajorRanking ranking = majorRankings.get(i);
                         %>
                         <tr style="<%=i % 2 == 0 ? "background: #fefbf3;" : "background: white;"%>">
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px;"><%=ranking.getRanking() > 0 ? ranking.getRanking() : "-"%></td>
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 500; font-size: 14px;"><%=Tools.view(ranking.getYxmc())%></td>
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px;">
                                 <%
                                 String level = ranking.getLevel();
                                 if (level != null && !level.trim().isEmpty()) {
                                     String levelColor = "";
                                     if (level.contains("A+")) levelColor = "background: #10b981; color: white;";
                                     else if (level.contains("A")) levelColor = "background: #3b82f6; color: white;";
                                     else if (level.contains("B+")) levelColor = "background: #f59e0b; color: white;";
                                     else if (level.contains("B")) levelColor = "background: #8b5cf6; color: white;";
                                     else levelColor = "background: #6b7280; color: white;";
                                 %>
                                 <span style="padding: 1mm 3mm; border-radius: 10pt; font-size: 9pt; font-weight: 500; <%=levelColor%>"><%=level%></span>
                                 <%
                                 } else {
                                 %>
                                 <span style="color: #6b7280;">-</span>
                                 <%
                                 }
                                 %>
                             </td>
                         </tr>
                         <%} %>
                     </tbody>
                 </table>
             </div>
             <%} else { %>
<%--              <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                 <i class="fas fa-medal" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_major())%> 专业软科排行
             </h3>
             <div class="info-box" style="background: linear-gradient(135deg, #fef3c7, #fed7aa);">
                 <div class="info-box-title" style="color: #92400e;"><i class="fas fa-exclamation-triangle"></i> 提示</div>
                 <div class="info-box-content" style="color: #92400e;">
                     暂无该专业的软科排行数据。
                 </div>
             </div> --%>
             <%} %>

             <!-- 专业学科评估排名 -->
             <%
             // 获取该专业在各院校的学科评估数据
             java.util.Map<String, String> majorXkpgMap = new java.util.HashMap<>();
             if (studentCard.getC_major() != null && !studentCard.getC_major().trim().isEmpty()) {
                 // 从所有院校专业标签数据中查找该专业的学科评估信息
                 java.util.HashMap<String, java.util.List<ZyzdBaseUniversityAndMajorTag>> allUniversityTags = DxCache.getAllUniversityMajorTagsMap();
                 for (java.util.Map.Entry<String, java.util.List<ZyzdBaseUniversityAndMajorTag>> entry : allUniversityTags.entrySet()) {
                     String universityName = entry.getKey();
                     java.util.List<ZyzdBaseUniversityAndMajorTag> tags = entry.getValue();
                     for (ZyzdBaseUniversityAndMajorTag tag : tags) {
                         if (tag.getZymc_org() != null && tag.getZymc_org().equals(studentCard.getC_major())
                             && tag.getXkpg() != null && !tag.getXkpg().trim().isEmpty() && !tag.getXkpg().equals("null")) {
                             majorXkpgMap.put(universityName, tag.getXkpg());
                             break; // 每个院校只取一条记录
                         }
                     }
                 }
             }

             if (!majorXkpgMap.isEmpty()) {
             %>
             <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                 <i class="fas fa-star" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_major())%> (专业学科评估 第四/五轮）
             </h3>

             <div style="overflow-x: auto; margin: 6mm 0;">
                 <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8pt; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
                     <thead style="background: linear-gradient(135deg, #10b981, #059669); color: white;">
                         <tr>
                             <th style="padding: 3mm; width: 20%;  text-align: left; font-weight: 700; border-bottom: 2pt solid #059669; font-size: 14px;">院校名称</th>
                             <th style="padding: 3mm; width: 40%; text-align: left; font-weight: 700; border-bottom: 2pt solid #059669; font-size: 14px;">学科评估等级</th>
                             <th style="padding: 3mm; width: 40%; text-align: left; font-weight: 700; border-bottom: 2pt solid #059669; font-size: 14px;">院校类型</th>
                         </tr>
                     </thead>
                     <tbody>
                         <%
                         // 对学科评估等级进行排序（A+最高，C-最低）
                         java.util.List<java.util.Map.Entry<String, String>> sortedEntries = new java.util.ArrayList<>(majorXkpgMap.entrySet());
                         sortedEntries.sort(new java.util.Comparator<java.util.Map.Entry<String, String>>() {
                             public int compare(java.util.Map.Entry<String, String> e1, java.util.Map.Entry<String, String> e2) {
                                 String level1 = e1.getValue();
                                 String level2 = e2.getValue();

                                 // 获取等级权重的方法
                                 int weight1 = 0;
                                 if (level1.contains("A+")) weight1 = 9;
                                 else if (level1.contains("A-")) weight1 = 7;
                                 else if (level1.contains("A")) weight1 = 8;
                                 else if (level1.contains("B+")) weight1 = 6;
                                 else if (level1.contains("B-")) weight1 = 4;
                                 else if (level1.contains("B")) weight1 = 5;
                                 else if (level1.contains("C+")) weight1 = 3;
                                 else if (level1.contains("C-")) weight1 = 1;
                                 else if (level1.contains("C")) weight1 = 2;

                                 int weight2 = 0;
                                 if (level2.contains("A+")) weight2 = 9;
                                 else if (level2.contains("A-")) weight2 = 7;
                                 else if (level2.contains("A")) weight2 = 8;
                                 else if (level2.contains("B+")) weight2 = 6;
                                 else if (level2.contains("B-")) weight2 = 4;
                                 else if (level2.contains("B")) weight2 = 5;
                                 else if (level2.contains("C+")) weight2 = 3;
                                 else if (level2.contains("C-")) weight2 = 1;
                                 else if (level2.contains("C")) weight2 = 2;

                                 return Integer.compare(weight2, weight1); // 降序排列
                             }
                         });

                         int rowIndex = 0;
                         for (java.util.Map.Entry<String, String> entry : sortedEntries) {
                             String universityName = entry.getKey();
                             String xkpgLevel = entry.getValue();
                             
                             ZyzdUniversityBean zyzdUniversityBean = DxCache.getUniversity(universityName);
                             
                         %>
                         <tr style="<%=rowIndex % 2 == 0 ? "background: #f0fdf4;" : "background: white;"%>">
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 500; font-size: 14px;"><%=Tools.view(universityName)%></td>
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px;">
                                 <%
                                 if (xkpgLevel != null && !xkpgLevel.trim().isEmpty()) {
                                     String levelColor = "";
                                     if (xkpgLevel.contains("A+")) levelColor = "background: #10b981; color: white;";
                                     else if (xkpgLevel.contains("A-")) levelColor = "background: #059669; color: white;";
                                     else if (xkpgLevel.contains("A")) levelColor = "background: #3b82f6; color: white;";
                                     else if (xkpgLevel.contains("B+")) levelColor = "background: #f59e0b; color: white;";
                                     else if (xkpgLevel.contains("B-")) levelColor = "background: #d97706; color: white;";
                                     else if (xkpgLevel.contains("B")) levelColor = "background: #8b5cf6; color: white;";
                                     else if (xkpgLevel.contains("C+")) levelColor = "background: #ef4444; color: white;";
                                     else if (xkpgLevel.contains("C-")) levelColor = "background: #dc2626; color: white;";
                                     else if (xkpgLevel.contains("C")) levelColor = "background: #f87171; color: white;";
                                     else levelColor = "background: #6b7280; color: white;";
                                 %>
                                 <span style="padding: 1mm 3mm; border-radius: 10pt; font-size: 10pt; font-weight: 600; <%=levelColor%>"><%=xkpgLevel%></span>
                                 <%
                                 } else {
                                 %>
                                 <span style="color: #6b7280;">-</span>
                                 <%
                                 }
                                 %>
                             </td>
                             
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; font-weight: 500;">
							    <%= zyzdUniversityBean != null ? Tools.view(zyzdUniversityBean.getYx_tags_all()) : "" %>
							</td>
                         </tr>
                         <%
                         rowIndex++;
                         } %>
                     </tbody>
                 </table>
             </div>

			<div style="margin-top: 4mm; padding: 4mm; background: #f8fafc; border-radius: 8pt; border-left: 4pt solid #8b5cf6;">
			  <div style="font-size: 11pt; color: #64748b; line-height: 1.6;">
			      <strong style="color: #581c87; font-size: 12pt;">学科评估等级说明：</strong>
			      <div style="margin-top: 3mm; display: flex; flex-wrap: wrap; gap: 2mm; align-items: center;">
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #10b981; color: white; white-space: nowrap;">A+</span> 
			          <span style="color: #374151; margin-right: 4mm;">前 2% 或前 2 名</span>
			          
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #3b82f6; color: white; white-space: nowrap;">A</span> 
			          <span style="color: #374151; margin-right: 4mm;">2%-5%</span>
			          
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #059669; color: white; white-space: nowrap;">A-</span> 
			          <span style="color: #374151; margin-right: 4mm;">5%-10%</span>
			          
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #f59e0b; color: white; white-space: nowrap;">B+</span> 
			          <span style="color: #374151; margin-right: 4mm;">10%-20%</span>
			          
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #8b5cf6; color: white; white-space: nowrap;">B</span> 
			          <span style="color: #374151; margin-right: 4mm;">20%-30%</span>
			      </div>
			      <div style="margin-top: 2mm; display: flex; flex-wrap: wrap; gap: 2mm; align-items: center;">
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #d97706; color: white; white-space: nowrap;">B-</span> 
			          <span style="color: #374151; margin-right: 4mm;">30%-40%</span>
			          
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #ef4444; color: white; white-space: nowrap;">C+</span> 
			          <span style="color: #374151; margin-right: 4mm;">40%-50%</span>
			          
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #f87171; color: white; white-space: nowrap;">C</span> 
			          <span style="color: #374151; margin-right: 4mm;">50%-60%</span>
			          
			          <span style="padding: 2mm 3mm; border-radius: 8pt; font-size: 9pt; font-weight: 700; background: #dc2626; color: white; white-space: nowrap;">C-</span> 
			          <span style="color: #374151;">60%-70%</span>
			      </div>
			  </div>
			</div>

             <%} %>

             <!-- 专业就业统计 -->
             <%if (majorEmployment != null && !majorEmployment.isEmpty()) { %>
             <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                 <i class="fas fa-chart-bar" style="margin-right: 3mm;"></i>该校 [ <%=Tools.view(studentCard.getC_major())%> ] 专业，就业统计（前20个行业）
             </h3>
             
             <div style="overflow-x: auto; margin: 6mm 0;">
                 <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8pt; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
                     <thead style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;">
                         <tr>
                             <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #7c3aed; font-size: 14px;">排名</th>
                             <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #7c3aed; font-size: 14px;">行业名称</th>
                             <th style="padding: 3mm; text-align: center; font-weight: 700; border-bottom: 2pt solid #7c3aed; font-size: 14px;">就业等级</th>
                         </tr>
                     </thead>
                     <tbody>
                         <%for (int i = 0; i < majorEmployment.size(); i++) { 
                             JiuyeBean employment = majorEmployment.get(i);
                             String level = getEmploymentLevel(employment.getCnt());
                             String levelStyle = getEmploymentLevelStyle(level);
                         %>
                         <tr style="<%=i % 2 == 0 ? "background: #f3e8ff;" : "background: white;"%>">
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 700; color: #7c3aed; font-size: 14px;"><%=i + 1%></td>
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 500; font-size: 14px;"><%=Tools.view(employment.getSshy())%></td>
                             <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; text-align: center; font-size: 14px;">
                                 <span style="<%=levelStyle%>"><%=level%></span>
                             </td>
                         </tr>
                         <%} %>
                     </tbody>
                 </table>
             </div>
             <%} else { %>
<%--              <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                 <i class="fas fa-chart-bar" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_major())%> 就业统计
             </h3>
             <div class="info-box" style="background: linear-gradient(135deg, #fef3c7, #fed7aa);">
                 <div class="info-box-title" style="color: #92400e;"><i class="fas fa-exclamation-triangle"></i> 提示</div>
                 <div class="info-box-content" style="color: #92400e;">
                     暂无该专业的就业统计数据。
                 </div>
             </div> --%>
             <%} %>
        </div>

        <div class="page-footer">
            <span><i class="fas fa-university"></i> 院校专业详细信息</span>
            <span>第 3 页</span>
        </div>
    </section>
    <%} %>

    <%
    // 查询招生计划和录取数据
    java.util.List<ZyzdMasterPlan> universityMasterPlans = null;
    java.util.List<ZyzdMasterPlan> majorMasterPlans = null;
    java.util.List<CareerUniversityMajorData> universityCareerData = null;
    java.util.List<CareerUniversityMajorData> majorCareerData = null;
    java.util.List<CareerKG> careerKGList = null;

    // 考研推荐功能变量定义
    int graduateRecommendationYear = 2024; // 考研推荐查询年份，方便后期修改
    List<CareerUniversityMajorData> currentUniversityMajorData = null;
    List<CareerUniversityMajorData> allMajorData = null;
    double averageScore = 0.0;
    List<Map<String, Object>> graduateRecommendations = new ArrayList<>();

    // 根据本科专业门类筛选的研究生专业名称列表
    java.util.List<String> matchedMasterMajorNames = new ArrayList<>();

    if (studentCard != null) {
        DaxueJdbc daxueJdbcNew = new DaxueJdbc();

        // 查询国考省考岗位数据
        if (studentCard.getC_major() != null && !Tools.isEmpty(studentCard.getC_major())) {
            careerKGList = daxueJdbcNew.getKgListByMajor(studentCard.getC_major());
        }

        // 根据本科专业获取门类，然后筛选研究生专业
        if (studentCard.getC_major() != null && !Tools.isEmpty(studentCard.getC_major())) {
            // 从缓存中获取本科专业信息
            ZyzdBaseMajor undergraduateMajor = DxCache.getMajor(studentCard.getC_major());
            if (undergraduateMajor != null && undergraduateMajor.getM_catg_one() != null) {
                // 根据门类获取匹配的研究生专业名称
                matchedMasterMajorNames = DxCache.getMasterMajorNamesByCategory(undergraduateMajor.getM_catg_one());
                Tools.println("本科专业: " + studentCard.getC_major() + ", 门类: " + undergraduateMajor.getM_catg_one() + ", 匹配的研究生专业数量: " + matchedMasterMajorNames.size());
            }
        }

        // 根据院校名称查询招生计划
        if (studentCard.getC_university() != null && !Tools.isEmpty(studentCard.getC_university())) {
            universityMasterPlans = daxueJdbcNew.listZyzdMasterPlanByYxmc(studentCard.getC_university());
            universityCareerData = daxueJdbcNew.listCareerUniversityMajorDataByYxmc(studentCard.getC_university());
        }

        // 根据专业名称查询招生计划
        if (studentCard.getC_major() != null && !Tools.isEmpty(studentCard.getC_major())) {
            majorMasterPlans = daxueJdbcNew.listZyzdMasterPlanByZymc(studentCard.getC_major());
            majorCareerData = daxueJdbcNew.listCareerUniversityMajorDataByZymc(studentCard.getC_major());
        }

        // 考研推荐功能 - 数据查询和计算
        if (studentCard.getC_university() != null && !Tools.isEmpty(studentCard.getC_university())
            && studentCard.getC_major() != null && !Tools.isEmpty(studentCard.getC_major())) {

            Tools.println("=== 开始考研推荐数据查询 ===");
            Tools.println("院校名称: " + studentCard.getC_university());
            Tools.println("专业名称: " + studentCard.getC_major());

            // 步骤1: 查询当前院校专业的考研数据
            currentUniversityMajorData = daxueJdbcNew.getUniversityMajorDataByYxmcAndZymc(graduateRecommendationYear, studentCard.getC_university(), studentCard.getC_major());
            Tools.println("当前院校专业考研数据条数: " + (currentUniversityMajorData != null ? currentUniversityMajorData.size() : 0));

            // 步骤2: 如果有数据，计算平均分
            if (currentUniversityMajorData != null && !currentUniversityMajorData.isEmpty()) {
                int totalSum = 0;
                for (CareerUniversityMajorData data : currentUniversityMajorData) {
                    totalSum += data.getTotal();
                }
                averageScore = (double) totalSum / currentUniversityMajorData.size();
                Tools.println("当前院校专业平均分: " + averageScore);

                // 步骤3: 查询所有院校该专业的考研数据
                allMajorData = daxueJdbcNew.listCareerUniversityMajorDataByZymc(graduateRecommendationYear, studentCard.getC_major());
                Tools.println("所有院校该专业考研数据条数: " + (allMajorData != null ? allMajorData.size() : 0));

                // 步骤4&5: 生成推荐列表（去重处理）
                if (allMajorData != null && !allMajorData.isEmpty()) {
                    // 使用Set进行去重，key为：学校名称+专业名称+分数
                    Set<String> uniqueKeys = new HashSet<>();
                    int index = 1;

                    for (CareerUniversityMajorData data : allMajorData) {
                        // 创建唯一标识：学校名称+专业名称+分数
                        String uniqueKey = data.getSchool_name() + "|" + data.getSpecial_name() + "|" + data.getTotal();

                        // 如果已存在相同的记录，跳过
                        if (uniqueKeys.contains(uniqueKey)) {
                            continue;
                        }
                        uniqueKeys.add(uniqueKey);

                        Map<String, Object> recommendation = new HashMap<>();
                        recommendation.put("index", index++);
                        recommendation.put("year", data.getData_year());
                        recommendation.put("schoolName", data.getSchool_name());
                        recommendation.put("specialName", data.getSpecial_name());
                        recommendation.put("totalScore", data.getTotal());

                        // 计算报考策略
                        String strategy;
                        double scoreDiff = averageScore - data.getTotal();
                        if (scoreDiff >= -5 && scoreDiff <= 10) {
                            strategy = "稳妥";
                        } else if (scoreDiff < -5) {
                            strategy = "冲一冲";
                        } else {
                            strategy = "保底";
                        }
                        recommendation.put("strategy", strategy);

                        graduateRecommendations.add(recommendation);

                        Tools.println("推荐院校: " + data.getSchool_name() + ", 专业: " + data.getSpecial_name() + ", 分数: " + data.getTotal() + ", 策略: " + strategy);
                    }
                    Tools.println("=== 考研推荐数据处理完成，去重后共生成 " + graduateRecommendations.size() + " 条推荐 ===");
                } else {
                    Tools.println("未找到该专业的考研数据，不显示考研推荐");
                }
            } else {
                Tools.println("当前院校专业无考研数据，不显示考研推荐");
            }
        }
    }
    %>

    <!-- 国考省考岗位信息 -->
    <%if (careerKGList != null && !careerKGList.isEmpty()) { %>
    <section class="page-section" id="section-career-positions">
        <div class="page-main-content">
            <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                <i class="fas fa-briefcase" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_major())%> （国考/省考岗位信息）
            </h3>

            <div style="overflow-x: auto; margin: 6mm 0;">
                <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8pt; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
                    <thead style="background: linear-gradient(135deg, #7c3aed, #6d28d9); color: white;">
                        <tr>
                            <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #6d28d9; font-size: 14px;">序号</th>
                            <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #6d28d9; font-size: 14px;">年份</th>
                            <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #6d28d9; font-size: 14px;">省份</th>
                            <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #6d28d9; font-size: 14px;">部门名称</th>
                        </tr>
                    </thead>
                    <tbody>
                        <%
                        for (int i = 0; i < Math.min(50, careerKGList.size()); i++) {
                            CareerKG careerKG = careerKGList.get(i);
                            String province = careerKG.getSf();
                            if (province == null || province.trim().isEmpty()) {
                                province = "国考";
                            }
                            String rowStyle = i % 2 == 0 ? "background: #f3f4f6;" : "background: white;";
                        %>
                        <tr style="<%=rowStyle%>">
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 700; color: #7c3aed; font-size: 14px;"><%=i + 1%></td>
                              <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 500; font-size: 14px;"><%=Tools.view(careerKG.getNf())%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 500; font-size: 14px;"><%=Tools.view(province)%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px;"><%=Tools.view(careerKG.getBmmc())%></td>
                        </tr>
                        <%} %>
                    </tbody>
                </table>
            </div>
        </div>
    </section>
    <%} %>

    <!-- 院校研究生招生计划 -->
    <%
    // 先检查是否有实际要显示的数据
    boolean hasUniversityPlanData = false;
    if (universityMasterPlans != null && !universityMasterPlans.isEmpty()) {
        for (int i = 0; i < universityMasterPlans.size(); i++) {
            ZyzdMasterPlan plan = universityMasterPlans.get(i);
            // 只显示匹配门类的专业，如果没有匹配的专业则显示所有专业
            if (matchedMasterMajorNames.isEmpty() || matchedMasterMajorNames.contains(plan.getZymc())) {
                hasUniversityPlanData = true;
                break;
            }
        }
    }

    if (hasUniversityPlanData) { %>
    <section class="page-section" id="section-graduate-admission">
        <div class="page-main-content">
            <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                <i class="fas fa-graduation-cap" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_university())%> 研究生招生计划
            </h3>

		<div style="overflow-x: auto; margin: 6mm 0;">
		  <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8pt; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
		      <thead style="background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;">
		          <tr>
		              <th style="padding: 2mm; width: 5%; text-align: left; font-weight: 700; font-size: 14px; border-bottom: 2pt solid #1e40af;">年份</th>
		              <th style="padding: 2mm; width: 15%; text-align: left; font-weight: 700; font-size: 14px; border-bottom: 2pt solid #1e40af;">学院名称</th>
		              <th style="padding: 2mm; width: 30%; text-align: left; font-weight: 700; font-size: 14px; border-bottom: 2pt solid #1e40af;">专业代码和名称</th>
		              <th style="padding: 2mm; width: 30%; text-align: left; font-weight: 700; font-size: 14px; border-bottom: 2pt solid #1e40af;">研究方向</th>
		              <th style="padding: 2mm; width: 20%; text-align: center; font-weight: 700; font-size: 14px; border-bottom: 2pt solid #1e40af;">招生人数</th>
		          </tr>
		      </thead>
		      <tbody>
		          <%
		          int displayRowIndex = 0;
		          for (int i = 0; i < universityMasterPlans.size(); i++) {
		              ZyzdMasterPlan plan = universityMasterPlans.get(i);
		              // 只显示匹配门类的专业，如果没有匹配的专业则显示所有专业
		              if (matchedMasterMajorNames.isEmpty() || matchedMasterMajorNames.contains(plan.getZymc())) {
		                  String rowStyle = displayRowIndex % 2 == 0 ? "background: #eff6ff;" : "background: white;";
		                  displayRowIndex++;
		          %>
		          <tr style="<%=rowStyle%>">
		              <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px;">2025</td>
		              <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px;"><%=Tools.view(plan.getXymc())%></td>
		              <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px;">
		                  <div style="color: #6b7280; font-size: 12px; font-weight: 600; margin-bottom: 2px;">[<%=Tools.view(plan.getZydm())%>]</div>
		                  <div style="color: #1f2937; font-size: 14px;"><%=Tools.view(plan.getZymc())%></div>
		              </td>
		              <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px;"><%=Tools.view(plan.getYjfx())%></td>
		              <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; text-align: center; color: #1e40af;"><%=Tools.view(plan.getZsrs())%></td>
		          </tr>
		          <%
		              }
		          } %>
		      </tbody>
		  </table>
		</div>
        </div>
    </section>
    <%} %>

    <!-- 专业招生计划 -->
    <%
    // 先检查是否有实际要显示的数据
    boolean hasMajorPlanData = false;
    if (majorMasterPlans != null && !majorMasterPlans.isEmpty()) {
        for (int i = 0; i < majorMasterPlans.size(); i++) {
            ZyzdMasterPlan plan = majorMasterPlans.get(i);
            // 只显示匹配门类的专业，如果没有匹配的专业则显示所有专业
            if (matchedMasterMajorNames.isEmpty() || matchedMasterMajorNames.contains(plan.getZymc())) {
                hasMajorPlanData = true;
                break;
            }
        }
    }

    if (hasMajorPlanData) { %>
    <section class="page-section">
        <div class="page-main-content">
            <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                <i class="fas fa-graduation-cap" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_major())%> 研究生招生计划
            </h3>

            <div style="overflow-x: auto; margin: 6mm 0;">
                <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8pt; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
                    <thead style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;">
                        <tr>
                            <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #7c3aed; font-size: 14px;">年份</th>
                            <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #7c3aed; font-size: 14px;">院校名称</th>
                            <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #7c3aed; font-size: 14px;">学院名称</th>
                            <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #7c3aed; font-size: 14px;">研究方向</th>
                            <th style="padding: 3mm; text-align: center; font-weight: 700; border-bottom: 2pt solid #7c3aed; font-size: 14px;">招生人数</th>
                            <th style="padding: 3mm; text-align: center; font-weight: 700; border-bottom: 2pt solid #7c3aed; font-size: 14px;">院校层次</th>
                        </tr>
                    </thead>
                    <tbody>
                        <%
                        int displayRowIndex2 = 0;
                        for (int i = 0; i < majorMasterPlans.size(); i++) {
                            ZyzdMasterPlan plan = majorMasterPlans.get(i);
                            // 只显示匹配门类的专业，如果没有匹配的专业则显示所有专业
                            if (matchedMasterMajorNames.isEmpty() || matchedMasterMajorNames.contains(plan.getZymc())) {
                                String rowStyle = displayRowIndex2 % 2 == 0 ? "background: #faf5ff;" : "background: white;";
                                displayRowIndex2++;
                        %>
                        <tr style="<%=rowStyle%>">
                         	<td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 500; font-size: 14px;">2025</td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 500; font-size: 14px;"><%=Tools.view(plan.getYxmc())%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 500; font-size: 14px;"><%=Tools.view(plan.getXymc())%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px;"><%=Tools.view(plan.getYjfx())%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; text-align: center; font-weight: 700; color: #7c3aed; font-size: 14px;"><%=Tools.view(plan.getZsrs())%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; text-align: center; font-size: 14px;"><%=Tools.view(plan.getYxlx())%></td>
                        </tr>
                        <%
                            }
                        } %>
                    </tbody>
                </table>
            </div>
        </div>
    </section>
    <%} %>

    <!-- 院校考研录取数据 -->
    <%
    // 先检查是否有实际要显示的数据
    boolean hasUniversityCareerData = false;
    if (universityCareerData != null && !universityCareerData.isEmpty()) {
        for (int i = 0; i < universityCareerData.size(); i++) {
            CareerUniversityMajorData data = universityCareerData.get(i);
            // 只显示匹配门类的专业，如果没有匹配的专业则显示所有专业
            if (matchedMasterMajorNames.isEmpty() || matchedMasterMajorNames.contains(data.getSpecial_name())) {
                hasUniversityCareerData = true;
                break;
            }
        }
    }

    if (hasUniversityCareerData) { %>
    <section class="page-section">
        <div class="page-main-content">
            <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                <i class="fas fa-chart-line" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_university())%> 考研录取数据
            </h3>

            <div style="overflow-x: auto; margin: 6mm 0;">
                <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8pt; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
                    <thead style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">
                        <tr>
                            <th style="padding: 3mm; text-align: center; font-weight: 700; border-bottom: 2pt solid #d97706; font-size: 14px;">年份</th>
                            <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #d97706; font-size: 14px;">学位类型</th>
                            <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #d97706; font-size: 14px;">专业名称</th>
                            <th style="padding: 3mm; text-align: center; font-weight: 700; border-bottom: 2pt solid #d97706; font-size: 14px;">总分</th>
                            <th style="padding: 3mm; text-align: center; font-weight: 700; border-bottom: 2pt solid #d97706; font-size: 14px;">政治</th>
                            <th style="padding: 3mm; text-align: center; font-weight: 700; border-bottom: 2pt solid #d97706; font-size: 14px;">英语</th>
                            <th style="padding: 3mm; text-align: center; font-weight: 700; border-bottom: 2pt solid #d97706; font-size: 14px;">专业一</th>
                            <th style="padding: 3mm; text-align: center; font-weight: 700; border-bottom: 2pt solid #d97706; font-size: 14px;">专业二</th>
                        </tr>
                    </thead>
                    <tbody>
                        <%
                        int displayRowIndex3 = 0;
                        for (int i = 0; i < universityCareerData.size(); i++) {
                            CareerUniversityMajorData data = universityCareerData.get(i);
                            // 只显示匹配门类的专业，如果没有匹配的专业则显示所有专业
                            if (matchedMasterMajorNames.isEmpty() || matchedMasterMajorNames.contains(data.getSpecial_name())) {
                                String rowStyle = displayRowIndex3 % 2 == 0 ? "background: #fffbeb;" : "background: white;";
                                displayRowIndex3++;
                        %>
                        <tr style="<%=rowStyle%>">
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; text-align: center; font-weight: 700; color: #d97706;"><%=data.getData_year()%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; font-weight: 500;"><%=Tools.view(data.getDegree_type_name())%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; font-weight: 500;"><%=Tools.view(data.getSpecial_name())%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; text-align: center; font-weight: 700;"><%=Tools.viewNumber(data.getTotal())%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; text-align: center;"><%=Tools.viewNumber(data.getPolitics())%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; text-align: center;"><%=Tools.viewNumber(data.getEnglish())%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; text-align: center;"><%=Tools.viewNumber(data.getSpecial_one())%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; text-align: center;"><%=Tools.viewNumber(data.getSpecial_two())%></td>
                        </tr>
                        <%
                            }
                        } %>
                    </tbody>
                </table>
            </div>
        </div>
    </section>
    <%} %>

    <!-- 考研专业录取数据 -->
    <%
    // 先检查是否有实际要显示的数据
    boolean hasDisplayData = false;
    if (majorCareerData != null && !majorCareerData.isEmpty()) {
        for (int i = 0; i < majorCareerData.size(); i++) {
            CareerUniversityMajorData data = majorCareerData.get(i);
            // 只显示匹配门类的专业，如果没有匹配的专业则显示所有专业
            if (matchedMasterMajorNames.isEmpty() || matchedMasterMajorNames.contains(data.getSpecial_name())) {
                hasDisplayData = true;
                break;
            }
        }
    }

    if (hasDisplayData) { %>
    <section class="page-section">
        <div class="page-main-content">
            <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">
                <i class="fas fa-chart-line" style="margin-right: 3mm;"></i><%=Tools.view(studentCard.getC_major())%> 考研录取数据
            </h3>

            <div style="overflow-x: auto; margin: 6mm 0;">
                <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8pt; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);">
                    <thead style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white;">
                        <tr>
                            <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #dc2626; font-size: 14px;">院校名称</th>
                            <th style="padding: 3mm; text-align: center; font-weight: 700; border-bottom: 2pt solid #dc2626; font-size: 14px;">年份</th>
                            <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #dc2626; font-size: 14px;">学位类型</th>
                            <th style="padding: 3mm; text-align: left; font-weight: 700; border-bottom: 2pt solid #dc2626; font-size: 14px;">专业名称</th>
                            <th style="padding: 3mm; text-align: center; font-weight: 700; border-bottom: 2pt solid #dc2626; font-size: 14px;">总分</th>
                            <th style="padding: 3mm; text-align: center; font-weight: 700; border-bottom: 2pt solid #dc2626; font-size: 14px;">政治</th>
                            <th style="padding: 3mm; text-align: center; font-weight: 700; border-bottom: 2pt solid #dc2626; font-size: 14px;">英语</th>
                            <th style="padding: 3mm; text-align: center; font-weight: 700; border-bottom: 2pt solid #dc2626; font-size: 14px;">院校类型</th>
                        </tr>
                    </thead>
                    <tbody>
                        <%
                        int displayRowIndex4 = 0;
                        ZyzdUniversityBean zyzdUniversityBean = new ZyzdUniversityBean();
                        for (int i = 0; i < majorCareerData.size(); i++) {
                            CareerUniversityMajorData data = majorCareerData.get(i);
                            // 只显示匹配门类的专业，如果没有匹配的专业则显示所有专业
                            if (matchedMasterMajorNames.isEmpty() || matchedMasterMajorNames.contains(data.getSpecial_name())) {
                                String rowStyle = displayRowIndex4 % 2 == 0 ? "background: #fef2f2;" : "background: white;";
                                displayRowIndex4++;

                                zyzdUniversityBean = DxCache.getUniversity(data.getSchool_name());
                        %>
                        <tr style="<%=rowStyle%>">
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; font-weight: 500;"><%=Tools.view(data.getSchool_name())%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; text-align: center; font-weight: 700; color: #dc2626;"><%=data.getData_year()%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; font-weight: 500;"><%=Tools.view(data.getDegree_type_name())%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; font-weight: 500;"><%=Tools.view(data.getSpecial_name())%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; text-align: center; font-weight: 700;"><%=Tools.viewNumber(data.getTotal())%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; text-align: center;"><%=Tools.viewNumber(data.getPolitics())%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; text-align: center;"><%=Tools.viewNumber(data.getEnglish())%></td>
							<td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-size: 14px; font-weight: 500;">
							    <%= zyzdUniversityBean != null ? Tools.view(zyzdUniversityBean.getYx_tags_all()) : "-" %>
							</td>
                        </tr>
                        <%
                            }
                        } %>
                    </tbody>
                </table>
            </div>
        </div>
    </section>
    <%} %>

    <!-- 考研推荐 -->
    <%if (graduateRecommendations != null && !graduateRecommendations.isEmpty()) { %>
    <section class="page-section" id="section-graduate-recommendations">
        <div class="page-main-content">
            <h2 class="title-section"><i class="fas fa-graduation-cap" style="margin-right: 3mm; color: #7c3aed;"></i>考研推荐</h2>
            <div class="table-container" style="width: 100%; overflow-x: auto;">
                <table class="data-table" style="width: 100%; border-collapse: collapse; margin: 0;">
                    <thead>
                        <tr style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                            <th style="padding: 4mm; border-bottom: 2pt solid #4c1d95; font-weight: 700; font-size: 16px; text-align: center; width: 8%;">序号</th>
                            <th style="padding: 4mm; border-bottom: 2pt solid #4c1d95; font-weight: 700; font-size: 16px; text-align: center; width: 12%;">年份</th>
                            <th style="padding: 4mm; border-bottom: 2pt solid #4c1d95; font-weight: 700; font-size: 16px; text-align: center; width: 30%;">学校名称</th>
                            <th style="padding: 4mm; border-bottom: 2pt solid #4c1d95; font-weight: 700; font-size: 16px; text-align: center; width: 25%;">专业名称</th>
                            <th style="padding: 4mm; border-bottom: 2pt solid #4c1d95; font-weight: 700; font-size: 16px; text-align: center; width: 12%;">总分</th>
                            <th style="padding: 4mm; border-bottom: 2pt solid #4c1d95; font-weight: 700; font-size: 16px; text-align: center; width: 13%;">报考策略</th>
                        </tr>
                    </thead>
                    <tbody>
                        <%
                        for (int i = 0; i < graduateRecommendations.size(); i++) {
                            Map<String, Object> recommendation = graduateRecommendations.get(i);
                            String rowStyle = i % 2 == 0 ? "background: #f8fafc;" : "background: white;";
                            String strategy = (String) recommendation.get("strategy");
                            String strategyColor = "";
                            String strategyBgColor = "";

                            if ("冲一冲".equals(strategy)) {
                                strategyColor = "#dc2626";
                                strategyBgColor = "#fef2f2";
                            } else if ("稳妥".equals(strategy)) {
                                strategyColor = "#059669";
                                strategyBgColor = "#f0fdf4";
                            } else if ("保底".equals(strategy)) {
                                strategyColor = "#d97706";
                                strategyBgColor = "#fffbeb";
                            }
                        %>
                        <tr style="<%=rowStyle%>">
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 700; color: #7c3aed; font-size: 14px; text-align: center;"><%=recommendation.get("index")%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 500; font-size: 14px; text-align: center;"><%=recommendation.get("year")%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 500; font-size: 14px;"><%=Tools.view((String)recommendation.get("schoolName"))%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 500; font-size: 14px;"><%=Tools.view((String)recommendation.get("specialName"))%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 700; font-size: 14px; text-align: center; color: #1f2937;"><%=recommendation.get("totalScore")%></td>
                            <td style="padding: 3mm; border-bottom: 1pt solid #e5e7eb; font-weight: 700; font-size: 14px; text-align: center; color: <%=strategyColor%>; background-color: <%=strategyBgColor%>; border-radius: 4px;"><%=strategy%></td>
                        </tr>
                        <%} %>
                    </tbody>
                </table>
            </div>
            <div style="margin-top: 4mm; padding: 3mm; background: #f3f4f6; border-radius: 8px; font-size: 14px; color: #6b7280;">
                <p style="margin: 0; font-weight: 500;">
                    <strong>策略说明：</strong>
                    <span style="color: #dc2626; font-weight: 600;">冲一冲</span> - 平均分比学校录取线低5-30分；
                    <span style="color: #059669; font-weight: 600;">稳妥</span> - 平均分比学校录取线低5分到高10分；
                    <span style="color: #d97706; font-weight: 600;">保底</span> - 平均分比学校录取线高10-30分
                </p>
                <p style="margin: 2mm 0 0 0; font-size: 13px;">
                    当前院校专业平均分：<strong style="color: #7c3aed;"><%=String.format("%.1f", averageScore)%></strong> 分
                </p>
            </div>
        </div>
    </section>
    <%} %>

    <!-- Section 3: 入学材料准备 -->
    <section class="page-section" id="section-admission-materials">
        <div class="page-main-content">
            <h2 class="title-section">入学材料准备</h2>
        
        <div class="card-grid">
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <h3 class="card-title">个人证件</h3>
                </div>
                <ul>
                    <li>录取通知书（必备）</li>
                    <li>身份证及复印件（多准备几份）</li>
                    <li>户口本及复印件（迁户口用）</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-camera"></i>
                    </div>
                    <h3 class="card-title">照片</h3>
                </div>
                <ul>
                    <li>一寸/二寸证件照（蓝/红底）</li>
                    <li>电子版照片（存U盘或云盘）</li>
                    <li>根据学校要求准备不同底色照片</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <h3 class="card-title">学生档案</h3>
                </div>
                <ul>
                    <li>高考准考证</li>
                    <li>学生档案袋（不要私自拆封）</li>
                    <li>成绩条（部分学校需要）</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-flag"></i>
                    </div>
                    <h3 class="card-title">党团组织关系</h3>
                </div>
                <ul>
                    <li>党团组织关系证明</li>
                    <li>团员证（团员必备）</li>
                    <li>介绍信（团员关系转接用）</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <h3 class="card-title">经济证明</h3>
                </div>
                <ul>
                    <li>家庭情况调查表</li>
                    <li>家庭经济困难证明（申请助学金用）</li>
                    <li>由乡（镇）、街道、民政部门开具证明并盖章</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <h3 class="card-title">其他特殊要求</h3>
                </div>
                <ul>
                    <li>兵役登记回执（符合条件的学生须完成网上兵役登记）</li>
                    <li>贷款助学相关资料（如需）</li>
                    <li>体检表（部分专业需要）</li>
                </ul>
            </div>
        </div>
        
        <div class="info-box">
            <div class="info-box-title"><i class="fas fa-info-circle"></i> 重要提示</div>
            <div class="info-box-content">
                所有材料请提前准备并整理好，建议使用文件袋分类存放，注明材料清单。入学前仔细阅读录取通知书中的报到须知，按学校要求准备特殊材料。
            </div>
        </div>
        </div>

        <div class="page-footer">
            <span><i class="fas fa-check-circle"></i> 入学材料准备清单</span>
            <span>第 5 页</span>
        </div>
    </section>

    <!-- Section 4: 生活用品准备 -->
    <section class="page-section" id="section-living-supplies">
        <div class="page-main-content">
            <h2 class="title-section">生活用品准备</h2>
        
        <div class="card-grid">
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-bed"></i>
                    </div>
                    <h3 class="card-title">床上用品</h3>
                </div>
                <ul>
                    <li>床单、被套、枕套（2-3套）</li>
                    <li>被子、枕头（可到校购买）</li>
                    <li>蚊帐（南方地区必备）</li>
                    <li>床垫、凉席（根据季节选择）</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-tshirt"></i>
                    </div>
                    <h3 class="card-title">衣物</h3>
                </div>
                <ul>
                    <li>四季衣物（根据当地气候准备）</li>
                    <li>内衣裤（多备几套）</li>
                    <li>运动服、运动鞋</li>
                    <li>正装（参加正式场合用）</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-shower"></i>
                    </div>
                    <h3 class="card-title">洗漱用品</h3>
                </div>
                <ul>
                    <li>牙刷、牙膏、漱口杯</li>
                    <li>洗发水、沐浴露、洗面奶</li>
                    <li>毛巾、浴巾</li>
                    <li>洗衣粉/洗衣液</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-laptop"></i>
                    </div>
                    <h3 class="card-title">学习用品</h3>
                </div>
                <ul>
                    <li>笔记本电脑（根据专业需求）</li>
                    <li>台灯、文具用品</li>
                    <li>笔记本、练习本</li>
                    <li>U盘、移动硬盘</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-plug"></i>
                    </div>
                    <h3 class="card-title">电器用品</h3>
                </div>
                <ul>
                    <li>手机充电器、数据线</li>
                    <li>插线板（多孔位）</li>
                    <li>小风扇（夏季必备）</li>
                    <li>热水壶（注意宿舍用电规定）</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-pills"></i>
                    </div>
                    <h3 class="card-title">常用药品</h3>
                </div>
                <ul>
                    <li>感冒药、退烧药</li>
                    <li>消炎药、创可贴</li>
                    <li>肠胃药、过敏药</li>
                    <li>体温计、医用口罩</li>
                </ul>
            </div>
        </div>
        
        <div class="info-box">
            <div class="info-box-title"><i class="fas fa-shopping-cart"></i> 购买建议</div>
            <div class="info-box-content">
                建议轻装上阵，必需品可到校后购买。大件物品（如被子、洗衣机等）可在当地购买，既节省运输成本又能适应当地环境。提前了解学校及周边购物环境。
            </div>
        </div>
        
        <div class="page-footer">
            <span><i class="fas fa-shopping-bag"></i> 生活用品清单</span>
            <span>第 4 页</span>
        </div>
    </section>

    <!-- Section 5: 心理准备 -->
    <section class="page-section" id="section-mental-preparation">


        <h2 class="title-section">心理准备</h2>
        
        <div class="card-grid-3">
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <h3 class="card-title">独立生活</h3>
                </div>
                <ul>
                    <li>自我管理能力培养</li>
                    <li>时间规划与执行</li>
                    <li>日常生活自理</li>
                    <li>财务规划意识</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-mountain"></i>
                    </div>
                    <h3 class="card-title">适应新环境</h3>
                </div>
                <ul>
                    <li>开放心态面对变化</li>
                    <li>建立新环境中的安全感</li>
                    <li>熟悉校园与周边环境</li>
                    <li>逐步适应独立生活</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="card-title">人际关系</h3>
                </div>
                <ul>
                    <li>宿舍关系处理技巧</li>
                    <li>主动社交，拓展人脉</li>
                    <li>尊重差异，求同存异</li>
                    <li>建立健康友谊圈</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="card-title">情绪管理</h3>
                </div>
                <ul>
                    <li>压力应对策略</li>
                    <li>挫折耐受力培养</li>
                    <li>积极情绪保持方法</li>
                    <li>寻求帮助的勇气</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 class="card-title">自我认知</h3>
                </div>
                <ul>
                    <li>重新定位自我价值</li>
                    <li>认识大学新角色</li>
                    <li>发掘个人潜能</li>
                    <li>建立大学阶段目标</li>
                </ul>
            </div>
            
            <div class="card" style="background: linear-gradient(135deg, #f0f9ff, #e0f2fe);">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3 class="card-title">心理准备建议</h3>
                </div>
                <ul>
                    <li>提前了解大学生活</li>
                    <li>做好心理预期准备</li>
                    <li>保持积极乐观心态</li>
                    <li>培养解决问题的能力</li>
                </ul>
            </div>
        </div>
        
        <div class="info-box">
            <div class="info-box-title"><i class="fas fa-quote-left"></i> 心理调适名言</div>
            <div class="info-box-content">
                "大学不仅是知识的殿堂，更是成长的沃土。适应新环境、建立新关系、管理新情绪，是每位新生必经的成长之路。"
            </div>
        </div>
        
        <div class="page-footer">
            <span><i class="fas fa-brain"></i> 心理准备指南</span>
            <span>第 6 页</span>
        </div>
    </section>

    <!-- Section 6: 学习准备 -->
    <section class="page-section" id="section-study-preparation">


        <h2 class="title-section">学习准备</h2>
        
        <div class="card-grid">
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <h3 class="card-title">学习方式转变</h3>
                </div>
                <ul>
                    <li><strong>被动学习 → 主动探究</strong></li>
                    <li>教师引导减少，自主性增强</li>
                    <li>注重理解而非死记硬背</li>
                    <li>培养批判性思维与创新能力</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="card-title">时间管理</h3>
                </div>
                <ul>
                    <li><strong>制定周/月学习计划</strong></li>
                    <li>合理分配课程学习时间</li>
                    <li>平衡学习与课外活动</li>
                    <li>利用碎片时间进行复习</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3 class="card-title">资源利用</h3>
                </div>
                <ul>
                    <li><strong>图书馆资源与数字资源</strong></li>
                    <li>学术数据库与期刊文献</li>
                    <li>在线学习平台与MOOC</li>
                    <li>教授办公时间与答疑机制</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-users-cog"></i>
                    </div>
                    <h3 class="card-title">学习方法</h3>
                </div>
                <ul>
                    <li><strong>小组学习与讨论</strong></li>
                    <li>概念图与思维导图整理</li>
                    <li>定期复习与知识整合</li>
                    <li>实践应用与项目参与</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3 class="card-title">专业认知</h3>
                </div>
                <ul>
                    <li><strong>了解专业课程设置</strong></li>
                    <li>研究专业发展方向与就业前景</li>
                    <li>参与专业相关讲座与活动</li>
                    <li>与学长学姐交流专业学习经验</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-book-reader"></i>
                    </div>
                    <h3 class="card-title">暑期准备</h3>
                </div>
                <div style="margin-bottom: 3mm;">
                    <strong>推荐阅读书目：</strong>
                </div>
                <div>
                    <span class="tag">《大学之路》</span>
                    <span class="tag">《如何学习》</span>
                    <span class="tag">专业入门书籍</span>
                    <span class="tag">经典文学作品</span>
                </div>
            </div>
        </div>
        
        <div class="info-box">
            <div class="info-box-title"><i class="fas fa-lightbulb"></i> 学习准备小贴士</div>
            <div class="info-box-content">
                大学学习强调自主性，建议提前了解目标专业的课程体系，培养独立思考能力。入学前可通过阅读相关书籍、参加线上课程等方式提前适应大学学习模式。
            </div>
        </div>
        
        <div class="page-footer">
            <span><i class="fas fa-book"></i> 学习准备指南</span>
            <span>第 7 页</span>
        </div>
    </section>

    <!-- Section 7: 专业准备要求 -->
    <section class="page-section" id="section-major-requirements">
         
        
        <h2 class="title-section">不同专业的特殊准备要求</h2>
        
        <div class="card-grid-3">
            <div class="card" style="border-left-color: #3b82f6;">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;">
                        <i class="fas fa-book"></i>
                    </div>
                    <h3 class="card-title" style="color: #1e40af;">文科专业</h3>
                </div>
                <div style="margin-bottom: 3mm;">
                    <span class="tag" style="background: #dbeafe; color: #1e40af;">经典阅读</span>
                    <span class="tag" style="background: #dbeafe; color: #1e40af;">思辨能力</span>
                    <span class="tag" style="background: #dbeafe; color: #1e40af;">竞赛准备</span>
                </div>
                <ul>
                    <li>提前阅读哲学、历史、文学经典著作</li>
                    <li>培养批判性思维与逻辑分析能力</li>
                    <li>关注社会热点，提升人文素养</li>
                    <li>准备相关专业竞赛（如辞论赛、作文比赛）</li>
                </ul>
            </div>
            
            <div class="card" style="border-left-color: #10b981;">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669); color: white;">
                        <i class="fas fa-flask"></i>
                    </div>
                    <h3 class="card-title" style="color: #065f46;">理科专业</h3>
                </div>
                <div style="margin-bottom: 3mm;">
                    <span class="tag" style="background: #d1fae5; color: #065f46;">基础学科</span>
                    <span class="tag" style="background: #d1fae5; color: #065f46;">科研项目</span>
                    <span class="tag" style="background: #d1fae5; color: #065f46;">数学能力</span>
                </div>
                <ul>
                    <li>巩固数学、物理、化学等基础学科知识</li>
                    <li>提升实验操作与数据分析能力</li>
                    <li>参与科研项目或学科竞赛</li>
                    <li>阅读前沿科技文献，了解学科发展</li>
                </ul>
            </div>
            
            <div class="card" style="border-left-color: #f59e0b;">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3 class="card-title" style="color: #92400e;">工科专业</h3>
                </div>
                <div style="margin-bottom: 3mm;">
                    <span class="tag" style="background: #fef3c7; color: #92400e;">专业软件</span>
                    <span class="tag" style="background: #fef3c7; color: #92400e;">创新实践</span>
                    <span class="tag" style="background: #fef3c7; color: #92400e;">行业动态</span>
                </div>
                <ul>
                    <li>提前学习专业相关软件（CAD、MATLAB等）</li>
                    <li>参与创新实践项目或工程比赛</li>
                    <li>关注行业前沿技术与发展趋势</li>
                    <li>培养团队协作与项目管理能力</li>
                </ul>
            </div>
            
            <div class="card" style="border-left-color: #ef4444;">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white;">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <h3 class="card-title" style="color: #991b1b;">医学专业</h3>
                </div>
                <div style="margin-bottom: 3mm;">
                    <span class="tag" style="background: #fee2e2; color: #991b1b;">医学基础</span>
                    <span class="tag" style="background: #fee2e2; color: #991b1b;">责任感</span>
                    <span class="tag" style="background: #fee2e2; color: #991b1b;">人文关怀</span>
                </div>
                <ul>
                    <li>提前了解人体解剖学、生理学基础知识</li>
                    <li>培养高度责任感与职业道德</li>
                    <li>提升沟通能力与人文关怀意识</li>
                    <li>关注医疗健康领域最新进展</li>
                </ul>
            </div>
            
            <div class="card" style="border-left-color: #8b5cf6;">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3 class="card-title" style="color: #581c87;">艺术专业</h3>
                </div>
                <div style="margin-bottom: 3mm;">
                    <span class="tag" style="background: #f3e8ff; color: #581c87;">作品集</span>
                    <span class="tag" style="background: #f3e8ff; color: #581c87;">专业技能</span>
                    <span class="tag" style="background: #f3e8ff; color: #581c87;">艺术理论</span>
                </div>
                <ul>
                    <li>精心准备专业作品集（3-5个项目）</li>
                    <li>持续提升绘画、设计等专业技能</li>
                    <li>学习艺术史与设计理论</li>
                    <li>培养创意思维与审美能力</li>
                </ul>
            </div>
            
            <div class="card" style="background: linear-gradient(135deg, #f0f9ff, #e0f2fe);">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #0ea5e9, #0284c7); color: white;">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3 class="card-title" style="color: #0c4a6e;">专业选择建议</h3>
                </div>
                <ul>
                    <li>提前了解专业课程设置与就业方向</li>
                    <li>参加专业体验活动或讲座</li>
                    <li>与相关领域从业者交流</li>
                    <li>结合个人兴趣与职业规划选择</li>
                </ul>
            </div>
        </div>
        
        <div class="info-box">
            <div class="info-box-title"><i class="fas fa-quote-left"></i> 专业准备金句</div>
            <div class="info-box-content">
                "选择专业不仅是选择一门学科，更是选择一种思维方式、一种生活方式和一种未来发展路径。"
            </div>
        </div>
        
        <div class="page-footer">
            <span><i class="fas fa-graduation-cap"></i> 专业准备指南</span>
            <span>第 8 页</span>
        </div>
    </section>

    <!-- Section 8: 入学手续办理 -->
    <section class="page-section" id="section-enrollment-procedures">


        <h2 class="title-section">入学手续办理指南</h2>
        
        <div class="card-grid">
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <h3 class="card-title">报到流程</h3>
                </div>
                <ul>
                    <li><strong>时间：</strong>按录取通知书指定时间</li>
                    <li><strong>地点：</strong>各学院迎新点/指定报到区</li>
                    <li><strong>材料：</strong>录取通知书、身份证、档案等</li>
                    <li><strong>步骤：</strong>验证→缴费→注册→领取物品</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <h3 class="card-title">宿舍安排</h3>
                </div>
                <ul>
                    <li><strong>申请：</strong>部分学校需提前在线申请</li>
                    <li><strong>物品：</strong>领取钥匙、空调遥控器等</li>
                    <li><strong>制度：</strong>遵守宿舍管理规定</li>
                    <li><strong>设施：</strong>熟悉宿舍公共设施位置</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <h3 class="card-title">校园卡办理</h3>
                </div>
                <ul>
                    <li><strong>功能：</strong>食堂、图书馆、门禁、消费</li>
                    <li><strong>地点：</strong>各校区校园卡服务中心</li>
                    <li><strong>充值：</strong>圈存机、微信、支付宝</li>
                    <li><strong>挂失：</strong>及时办理挂失与补办手续</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-stethoscope"></i>
                    </div>
                    <h3 class="card-title">体检安排</h3>
                </div>
                <ul>
                    <li><strong>时间：</strong>入学后1-2周内</li>
                    <li><strong>地点：</strong>校医院或指定医疗机构</li>
                    <li><strong>项目：</strong>常规体检、传染病筛查</li>
                    <li><strong>注意：</strong>空腹、携带身份证和照片</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-running"></i>
                    </div>
                    <h3 class="card-title">军训安排</h3>
                </div>
                <ul>
                    <li><strong>时间：</strong>通常为入学后2-3周</li>
                    <li><strong>服装：</strong>统一发放军训服装</li>
                    <li><strong>要求：</strong>遵守纪律、注意安全</li>
                    <li><strong>准备：</strong>防晒用品、水杯、舒适鞋袜</li>
                </ul>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <h3 class="card-title">入学教育</h3>
                </div>
                <ul>
                    <li><strong>校史校情：</strong>了解学校历史与文化</li>
                    <li><strong>专业教育：</strong>认识专业前景与发展</li>
                    <li><strong>安全教育：</strong>消防安全、人身财产安全</li>
                    <li><strong>规章制度：</strong>校规校纪、学分制度等</li>
                </ul>
            </div>
        </div>
        
        <div class="timeline">
            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date"><i class="fas fa-calendar-check"></i> 8月底-9月初</div>
                    <div class="timeline-title">新生报到日</div>
                    <div class="timeline-description">
                        携带录取通知书、身份证等材料，前往学校指定地点完成报到手续，领取宿舍钥匙和校园卡。
                    </div>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date"><i class="fas fa-running"></i> 9月初</div>
                    <div class="timeline-title">军训开始</div>
                    <div class="timeline-description">
                        为期2-3周的军事训练，培养纪律意识和团队精神，是大学生活的第一课。
                    </div>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date"><i class="fas fa-graduation-cap"></i> 9月中旬</div>
                    <div class="timeline-title">开学典礼</div>
                    <div class="timeline-description">
                        参加隆重的开学典礼，聆听校长致辞，正式成为大学的一员，开启人生新篇章。
                    </div>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date"><i class="fas fa-book-open"></i> 9月中下旬</div>
                    <div class="timeline-title">选课系统开放</div>
                    <div class="timeline-description">
                        登录教务系统选择本学期课程，合理规划课表，为四年学习打下良好基础。
                    </div>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date"><i class="fas fa-chalkboard-teacher"></i> 9月底</div>
                    <div class="timeline-title">正式上课</div>
                    <div class="timeline-description">
                        开始大学第一堂课，适应新的学习节奏和教学方式，开启知识探索之旅。
                    </div>
                </div>
            </div>
        </div>
        
        <div class="page-footer">
            <span><i class="fas fa-tasks"></i> 入学手续办理指南</span>
            <span>第 9 页</span>
        </div>
    </section>

    <!-- Section 9: 常见问题解答 -->
    <section class="page-section" id="section-faq">
         
        
        <h2 class="title-section">常见问题与解决方案</h2>
        
        <div class="card-grid">
            <div class="card" style="border-left-color: #3b82f6;">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;">
                        <i class="fas fa-book"></i>
                    </div>
                    <h3 class="card-title" style="color: #1e40af;">如何选择课程</h3>
                </div>
                <div style="margin-bottom: 3mm;">
                    <span class="tag" style="background: #dbeafe; color: #1e40af;">了解选课系统</span>
                    <span class="tag" style="background: #dbeafe; color: #1e40af;">咨询学长学姐</span>
                    <span class="tag" style="background: #dbeafe; color: #1e40af;">考虑学分要求</span>
                </div>
                <ul>
                    <li>提前熟悉选课流程与时间节点</li>
                    <li>参考学长学姐的课程评价与建议</li>
                    <li>平衡必修课与选修课的比例</li>
                    <li>考虑课程难度与个人兴趣匹配度</li>
                </ul>
            </div>
            
            <div class="card" style="border-left-color: #10b981;">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669); color: white;">
                        <i class="fas fa-home"></i>
                    </div>
                    <h3 class="card-title" style="color: #065f46;">如何适应宿舍生活</h3>
                </div>
                <div style="margin-bottom: 3mm;">
                    <span class="tag" style="background: #d1fae5; color: #065f46;">处理室友关系</span>
                    <span class="tag" style="background: #d1fae5; color: #065f46;">调整作息</span>
                    <span class="tag" style="background: #d1fae5; color: #065f46;">解决冲突</span>
                </div>
                <ul>
                    <li>建立宿舍公约，明确生活习惯</li>
                    <li>尊重彼此隐私与个人空间</li>
                    <li>主动沟通，及时解决矛盾</li>
                    <li>积极参与宿舍集体活动</li>
                </ul>
            </div>
            
            <div class="card" style="border-left-color: #f59e0b;">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <h3 class="card-title" style="color: #92400e;">如何平衡学习与社交</h3>
                </div>
                <div style="margin-bottom: 3mm;">
                    <span class="tag" style="background: #fef3c7; color: #92400e;">时间管理</span>
                    <span class="tag" style="background: #fef3c7; color: #92400e;">优先级设置</span>
                    <span class="tag" style="background: #fef3c7; color: #92400e;">自我调节</span>
                </div>
                <ul>
                    <li>制定周计划与日计划</li>
                    <li>区分学习任务优先级</li>
                    <li>合理安排社交活动时间</li>
                    <li>学会拒绝，避免过度承诺</li>
                </ul>
            </div>
            
            <div class="card" style="border-left-color: #ef4444;">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white;">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 class="card-title" style="color: #991b1b;">如何应对学习压力</h3>
                </div>
                <div style="margin-bottom: 3mm;">
                    <span class="tag" style="background: #fee2e2; color: #991b1b;">学习方法调整</span>
                    <span class="tag" style="background: #fee2e2; color: #991b1b;">心理调适</span>
                    <span class="tag" style="background: #fee2e2; color: #991b1b;">寻求帮助</span>
                </div>
                <ul>
                    <li>改进学习方法，提高效率</li>
                    <li>适当放松，保持身心健康</li>
                    <li>与同学交流学习经验</li>
                    <li>必要时寻求老师或心理咨询帮助</li>
                </ul>
            </div>
            
            <div class="card" style="border-left-color: #8b5cf6;">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;">
                        <i class="fas fa-route"></i>
                    </div>
                    <h3 class="card-title" style="color: #581c87;">如何规划大学生涯</h3>
                </div>
                <div style="margin-bottom: 3mm;">
                    <span class="tag" style="background: #f3e8ff; color: #581c87;">了解专业路径</span>
                    <span class="tag" style="background: #f3e8ff; color: #581c87;">参与实践活动</span>
                    <span class="tag" style="background: #f3e8ff; color: #581c87;">明确目标</span>
                </div>
                <ul>
                    <li>了解专业发展方向与就业前景</li>
                    <li>参与科研项目与学科竞赛</li>
                    <li>实习与社会实践相结合</li>
                    <li>制定短期与长期目标</li>
                </ul>
            </div>
            
            <div class="card" style="border-left-color: #ec4899;">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #ec4899, #db2777); color: white;">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="card-title" style="color: #be185d;">如何处理想家情绪</h3>
                </div>
                <div style="margin-bottom: 3mm;">
                    <span class="tag" style="background: #fce7f3; color: #be185d;">建立新社交圈</span>
                    <span class="tag" style="background: #fce7f3; color: #be185d;">保持联系</span>
                    <span class="tag" style="background: #fce7f3; color: #be185d;">积极适应</span>
                </div>
                <ul>
                    <li>主动参与校园活动，结交新朋友</li>
                    <li>定期与家人朋友保持联系</li>
                    <li>探索校园及周边环境</li>
                    <li>培养新的兴趣爱好</li>
                </ul>
            </div>
        </div>
        
        <div class="info-box">
            <div class="info-box-title"><i class="fas fa-lightbulb"></i> 专家建议</div>
            <div class="info-box-content">
                "大学是自我成长的重要阶段，遇到问题是正常的。保持开放心态，主动寻求解决方案，培养独立解决问题的能力，是大学生活的重要一课。"
            </div>
        </div>
        
        <div class="page-footer">
            <span><i class="fas fa-question-circle"></i> 常见问题与解决方案</span>
            <span>第 10 页</span>
        </div>
    </section>

    <!-- Section 10: 四年规划指南 -->
    <section class="page-section" id="section-four-year-plan">


        <h2 class="title-section">大学四年规划指南</h2>

        <!-- 大一规划 -->
        <div class="timeline-item">
            <div class="timeline-content">
                <div class="timeline-date"><i class="fas fa-seedling"></i> 大一阶段规划 - 大一小白阶段</div>
                <div class="timeline-title">📚 学期目标规划</div>
                <div class="timeline-description">
                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #3b82f6;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <h3 class="card-title" style="color: #1e40af;">上学期主要目标</h3>
                            </div>
                            <ul>
                                <li>适应大学生活，建立良好的学习习惯</li>
                                <li>掌握专业基础课程，夯实理论基础</li>
                                <li>培养专业思维，了解学科特点</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #10b981;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669); color: white;">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <h3 class="card-title" style="color: #065f46;">下学期主要目标</h3>
                            </div>
                            <ul>
                                <li>深入学习专业核心课程</li>
                                <li>按培养方案要求通过大学英语四级或校内学位英语考试</li>
                                <li>参加社团活动，提升综合素质</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #f59e0b;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">
                                    <i class="fas fa-tasks"></i>
                                </div>
                                <h3 class="card-title" style="color: #92400e;">关键任务</h3>
                            </div>
                            <ul>
                                <li>保持GPA在3.5以上</li>
                                <li>完成第一个专业实践项目</li>
                                <li>建立学习小组</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #8b5cf6;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <h3 class="card-title" style="color: #581c87;">技能发展重点</h3>
                            </div>
                            <ul>
                                <li>专业基础技能</li>
                                <li>逻辑思维能力</li>
                                <li>自主学习能力</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #ef4444;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white;">
                                    <i class="fas fa-calendar-week"></i>
                                </div>
                                <h3 class="card-title" style="color: #991b1b;">假期建议</h3>
                            </div>
                            <ul>
                                <li><strong>🎿 寒假：</strong>复习上学期课程，预习下学期内容</li>
                                <li><strong>🏖️ 暑假：</strong>参加暑期专业实践活动，做第一个完整项目</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 大二规划 -->
        <div class="timeline-item">
            <div class="timeline-content">
                <div class="timeline-date"><i class="fas fa-rocket"></i> 大二阶段规划 - 大二拉开差距，埋下伏笔</div>
                <div class="timeline-title">🚀 学期目标规划</div>
                <div class="timeline-description">
                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #3b82f6;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <h3 class="card-title" style="color: #1e40af;">上学期主要目标</h3>
                            </div>
                            <ul>
                                <li>深入学习专业核心理论</li>
                                <li>掌握专业核心技能</li>
                                <li>开始参与竞赛活动</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #10b981;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669); color: white;">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <h3 class="card-title" style="color: #065f46;">下学期主要目标</h3>
                            </div>
                            <ul>
                                <li>学习专业进阶课程</li>
                                <li>通过英语六级考试</li>
                                <li>完成第一个团队项目</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #f59e0b;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">
                                    <i class="fas fa-search"></i>
                                </div>
                                <h3 class="card-title" style="color: #92400e;">探索方向</h3>
                            </div>
                            <ul>
                                <li>专业细分方向A</li>
                                <li>专业细分方向B</li>
                                <li>跨学科发展方向</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #8b5cf6;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;">
                                    <i class="fas fa-flask"></i>
                                </div>
                                <h3 class="card-title" style="color: #581c87;">科研机会</h3>
                            </div>
                            <ul>
                                <li>参与导师科研项目</li>
                                <li>申请大学生创新创业项目</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #ef4444;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white;">
                                    <i class="fas fa-trophy"></i>
                                </div>
                                <h3 class="card-title" style="color: #991b1b;">推荐竞赛</h3>
                            </div>
                            <ul>
                                <li>专业相关竞赛</li>
                                <li>学科技能大赛</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin-top: 6mm;">
                        <span class="tag">🎿 寒假建议：深入学习专业知识，准备竞赛</span>
                        <span class="tag">🏖️ 暑假建议：第一次实习体验，了解行业需求</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="page-footer">
            <span><i class="fas fa-calendar-alt"></i> 四年规划指南</span>
            <span>第 11 页</span>
        </div>
    </section>

    <!-- Section 11: 四年规划指南（续） -->
    <section class="page-section">
         

        <!-- 大三规划 -->
        <div class="timeline-item">
            <div class="timeline-content">
                <div class="timeline-date"><i class="fas fa-target"></i> 大三阶段规划 - 大三马拉松的冲刺阶段</div>
                <div class="timeline-title">🎯 学期目标规划</div>
                <div class="timeline-description">
                    <div class="card-grid">
                        <div class="card" style="border-left-color: #3b82f6;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <h3 class="card-title" style="color: #1e40af;">上学期主要目标</h3>
                            </div>
                            <ul>
                                <li>完成核心专业课程学习</li>
                                <li>确定毕业设计方向</li>
                                <li>开始准备实习简历</li>
                            </ul>
                        </div>

                        <div class="card" style="border-left-color: #10b981;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669); color: white;">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <h3 class="card-title" style="color: #065f46;">下学期主要目标</h3>
                            </div>
                            <ul>
                                <li>获得心仪单位实习机会</li>
                                <li>参与重要项目实践</li>
                                <li>准备就业或深造材料</li>
                            </ul>
                        </div>
                    </div>

                    <div class="card-grid">
                        <div class="card" style="border-left-color: #f59e0b;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">
                                    <i class="fas fa-book"></i>
                                </div>
                                <h3 class="card-title" style="color: #92400e;">学术重点</h3>
                            </div>
                            <ul>
                                <li>专业项目管理</li>
                                <li>专业核心技能深化</li>
                                <li>前沿知识学习</li>
                            </ul>
                        </div>

                        <div class="card" style="border-left-color: #8b5cf6;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;">
                                    <i class="fas fa-globe"></i>
                                </div>
                                <h3 class="card-title" style="color: #581c87;">语言技能</h3>
                            </div>
                            <ul>
                                <li>提升英语口语能力</li>
                                <li>学习专业英语文档阅读</li>
                            </ul>
                        </div>
                    </div>

                    <div class="card-grid" style="margin-top: 6mm;">
                        <div class="card" style="border-left-color: #ef4444;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white;">
                                    <i class="fas fa-edit"></i>
                                </div>
                                <h3 class="card-title" style="color: #991b1b;">论文发表</h3>
                            </div>
                            <ul>
                                <li>参与导师论文研究</li>
                                <li>撰写毕业设计相关论文</li>
                            </ul>
                        </div>

                        <div class="card" style="border-left-color: #06b6d4;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2); color: white;">
                                    <i class="fas fa-clipboard-list"></i>
                                </div>
                                <h3 class="card-title" style="color: #0e7490;">申请材料</h3>
                            </div>
                            <ul>
                                <li>完善个人简历</li>
                                <li>准备作品集</li>
                                <li>收集推荐信</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin-top: 6mm;">
                        <span class="tag">🎿 寒假建议：实习准备，专业技能深度学习</span>
                        <span class="tag">🏖️ 暑假建议：在优质单位实习，积累项目经验</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 大四规划 -->
        <div class="timeline-item">
            <div class="timeline-content">
                <div class="timeline-date"><i class="fas fa-graduation-cap"></i> 大四阶段规划 - 大四收获的季节</div>
                <div class="timeline-title">🎓 学期目标规划</div>
                <div class="timeline-description">
                    <div class="card-grid">
                        <div class="card" style="border-left-color: #3b82f6;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <h3 class="card-title" style="color: #1e40af;">上学期主要目标</h3>
                            </div>
                            <ul>
                                <li>完成毕业设计开题</li>
                                <li>投递心仪公司岗位</li>
                                <li>参加校园招聘</li>
                            </ul>
                        </div>

                        <div class="card" style="border-left-color: #10b981;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669); color: white;">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <h3 class="card-title" style="color: #065f46;">下学期主要目标</h3>
                            </div>
                            <ul>
                                <li>完成毕业设计答辩</li>
                                <li>确定工作offer</li>
                                <li>顺利毕业</li>
                            </ul>
                        </div>
                    </div>

                    <div class="card-grid">
                        <div class="card" style="border-left-color: #f59e0b;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <h3 class="card-title" style="color: #92400e;">申请流程</h3>
                            </div>
                            <ul>
                                <li>秋季校园招聘</li>
                                <li>春季补招</li>
                                <li>社会招聘</li>
                            </ul>
                        </div>

                        <div class="card" style="border-left-color: #8b5cf6;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;">
                                    <i class="fas fa-book-open"></i>
                                </div>
                                <h3 class="card-title" style="color: #581c87;">考试准备</h3>
                            </div>
                            <ul>
                                <li>毕业设计答辩准备</li>
                                <li>专业面试准备</li>
                            </ul>
                        </div>
                    </div>

                    <div class="card-grid" style="margin-top: 6mm;">
                        <div class="card" style="border-left-color: #ef4444;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white;">
                                    <i class="fas fa-briefcase"></i>
                                </div>
                                <h3 class="card-title" style="color: #991b1b;">求职活动</h3>
                            </div>
                            <ul>
                                <li>参加招聘会</li>
                                <li>网络投递简历</li>
                                <li>内推机会把握</li>
                            </ul>
                        </div>

                        <div class="card" style="border-left-color: #06b6d4;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2); color: white;">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <h3 class="card-title" style="color: #0e7490;">毕业要求</h3>
                            </div>
                            <ul>
                                <li>修满学分要求</li>
                                <li>通过毕业设计</li>
                                <li>完成实习学分</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin-top: 6mm;">
                        <span class="tag">🎿 寒假建议：准备春招，完善毕业设计</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="page-footer">
            <span><i class="fas fa-calendar-alt"></i> 四年规划指南（续）</span>
            <span>第 12 页</span>
        </div>
    </section>

    <!-- Section 12: 考证与竞赛规划 -->
    <section class="page-section">
         

        <h2 class="title-section">大学四年考证规划</h2>

        <!-- 大一大二阶段考证 -->
        <div class="timeline-item">
            <div class="timeline-content">
                <div class="timeline-date"><i class="fas fa-certificate"></i> 大一大二阶段考证</div>
                <div class="timeline-title">基础证书与技能认证</div>
                <div class="timeline-description">
                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #ef4444;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white;">
                                    <i class="fas fa-language"></i>
                                </div>
                                <h3 class="card-title" style="color: #991b1b;">英语四六级(CET)</h3>
                                <span class="tag" style="background: #fee2e2; color: #991b1b;">必考</span>
                            </div>
                            <ul>
                                <li>企业衡量毕业生英语水平的重要标准</li>
                                <li>2025年上半年笔试在6月14日，口试在5月24-25日</li>
                                <li>考试安排：通常3月和9月报名</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #10b981;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669); color: white;">
                                    <i class="fas fa-microphone"></i>
                                </div>
                                <h3 class="card-title" style="color: #065f46;">普通话证书</h3>
                                <span class="tag" style="background: #d1fae5; color: #065f46;">推荐</span>
                            </div>
                            <ul>
                                <li>师范、播音、主持等职业必需</li>
                                <li>部分省市对公务员也有要求</li>
                                <li>考试安排：全年可考，各地时间不同</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #3b82f6;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;">
                                    <i class="fas fa-laptop"></i>
                                </div>
                                <h3 class="card-title" style="color: #1e40af;">全国计算机等级考试(NCRE)</h3>
                                <span class="tag" style="background: #dbeafe; color: #1e40af;">推荐</span>
                            </div>
                            <ul>
                                <li>计算机应用知识与技能的全国性考试</li>
                                <li>2025年春季考试在3月29-31日</li>
                                <li>考试安排：通常在考前2-3个月报名</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #f59e0b;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">
                                    <i class="fas fa-car"></i>
                                </div>
                                <h3 class="card-title" style="color: #92400e;">驾驶证</h3>
                                <span class="tag" style="background: #fef3c7; color: #92400e;">建议</span>
                            </div>
                            <ul>
                                <li>大学阶段时间充足，是最适合考驾照的时期</li>
                                <li>考试安排：自行通过驾校预约</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #8b5cf6;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;">
                                    <i class="fas fa-calculator"></i>
                                </div>
                                <h3 class="card-title" style="color: #581c87;">初级会计</h3>
                                <span class="tag" style="background: #f3e8ff; color: #581c87;">专业相关</span>
                            </div>
                            <ul>
                                <li>会计专业学生首选，其他专业可选</li>
                                <li>考试安排：通常在11月份报名，次年5月考试</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 大二大三阶段考证 -->
        <div class="timeline-item">
            <div class="timeline-content">
                <div class="timeline-date"><i class="fas fa-award"></i> 大二大三阶段考证</div>
                <div class="timeline-title">专业进阶证书</div>
                <div class="timeline-description">
                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #3b82f6;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;">
                                    <i class="fas fa-language"></i>
                                </div>
                                <h3 class="card-title" style="color: #1e40af;">英语专业八级</h3>
                                <span class="tag" style="background: #dbeafe; color: #1e40af;">英语专业</span>
                            </div>
                            <ul>
                                <li>英语专业及英语辅修/双学位学生可报考</li>
                                <li>考试安排：每年3月上旬</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #10b981;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669); color: white;">
                                    <i class="fas fa-globe"></i>
                                </div>
                                <h3 class="card-title" style="color: #065f46;">翻译证</h3>
                                <span class="tag" style="background: #d1fae5; color: #065f46;">语言相关</span>
                            </div>
                            <ul>
                                <li>全国翻译专业资格水平认证</li>
                                <li>考试安排：每年5月和10月</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #f59e0b;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">
                                    <i class="fas fa-map-marked-alt"></i>
                                </div>
                                <h3 class="card-title" style="color: #92400e;">导游证</h3>
                                <span class="tag" style="background: #fef3c7; color: #92400e;">旅游管理</span>
                            </div>
                            <ul>
                                <li>旅游管理专业必考，其他专业可选</li>
                                <li>考试安排：6-8月报名，11月笔试</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 大三大四阶段考证 -->
        <div class="timeline-item">
            <div class="timeline-content">
                <div class="timeline-date"><i class="fas fa-medal"></i> 大三大四阶段考证</div>
                <div class="timeline-title">高级专业证书</div>
                <div class="timeline-description">
                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #ef4444;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white;">
                                    <i class="fas fa-chalkboard-teacher"></i>
                                </div>
                                <h3 class="card-title" style="color: #991b1b;">教师资格证</h3>
                                <span class="tag" style="background: #fee2e2; color: #991b1b;">教育相关</span>
                            </div>
                            <ul>
                                <li>教育行业从业必备，含金量高</li>
                                <li>上半年1月12-15日报名，3月8日笔试</li>
                                <li>下半年9月报名，11月笔试</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #8b5cf6;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <h3 class="card-title" style="color: #581c87;">注册会计师(CPA)</h3>
                                <span class="tag" style="background: #f3e8ff; color: #581c87;">会计专业</span>
                            </div>
                            <ul>
                                <li>财会领域黄金职业证书</li>
                                <li>2025年专业阶段考试在8月22-24日</li>
                                <li>考试安排：通常在4月报名</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #06b6d4;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2); color: white;">
                                    <i class="fas fa-balance-scale"></i>
                                </div>
                                <h3 class="card-title" style="color: #0e7490;">法律职业资格证</h3>
                                <span class="tag" style="background: #cffafe; color: #0e7490;">法学专业</span>
                            </div>
                            <ul>
                                <li>法官、检察官、律师等职业必需</li>
                                <li>考试安排：6-7月报名，9月考试</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #10b981;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669); color: white;">
                                    <i class="fas fa-heartbeat"></i>
                                </div>
                                <h3 class="card-title" style="color: #065f46;">护士资格证</h3>
                                <span class="tag" style="background: #d1fae5; color: #065f46;">医学专业</span>
                            </div>
                            <ul>
                                <li>护理专业必考</li>
                                <li>考试安排：12月报名，次年考试</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="page-footer">
            <span><i class="fas fa-certificate"></i> 考证规划指南</span>
            <span>第 13 页</span>
        </div>
    </section>

    <!-- Section 13: 高含金量竞赛规划 -->
    <section class="page-section">
         

        <h2 class="title-section">高含金量竞赛规划</h2>

        <!-- 全国性权威竞赛 -->
        <div class="card" style="border-left-color: #f59e0b; background: linear-gradient(135deg, #fef3c7, #fbbf24);">
            <div class="card-header">
                <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">
                    <i class="fas fa-trophy"></i>
                </div>
                <h3 class="card-title" style="color: #92400e;">全国性权威竞赛</h3>
            </div>

            <div style="margin: 6mm 0;">
                <div class="card" style="border-left-color: #3b82f6;">
                    <div class="card-header">
                        <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <h3 class="card-title" style="color: #1e40af;">全国大学生数学建模竞赛</h3>
                    </div>
                    <ul>
                        <li><strong>时间：</strong>9月4日-7日（具体以当年通知为准）</li>
                        <li><strong>报名截止：</strong>通常在8月底</li>
                        <li><strong>类别：</strong>数学</li>
                        <li><strong>难度：</strong>高</li>
                        <li>数学建模权威赛事，保研加分重要竞赛</li>
                    </ul>
                </div>
            </div>

            <div style="margin: 6mm 0;">
                <div class="card" style="border-left-color: #10b981;">
                    <div class="card-header">
                        <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669); color: white;">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <h3 class="card-title" style="color: #065f46;">中国国际大学生创新大赛</h3>
                    </div>
                    <ul>
                        <li><strong>时间：</strong>4月-10月</li>
                        <li><strong>校赛阶段：</strong>通常在5-7月</li>
                        <li><strong>省赛国赛：</strong>8-10月</li>
                        <li><strong>类别：</strong>创业</li>
                        <li><strong>难度：</strong>高</li>
                        <li>国家级创新创业平台，影响力极大</li>
                    </ul>
                </div>
            </div>

            <div style="margin: 6mm 0;">
                <div class="card" style="border-left-color: #ef4444;">
                    <div class="card-header">
                        <div class="card-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white;">
                            <i class="fas fa-flask"></i>
                        </div>
                        <h3 class="card-title" style="color: #991b1b;">"挑战杯"系列竞赛</h3>
                    </div>
                    <ul>
                        <li><strong>赛事安排：</strong>大学生课外学术科技作品竞赛（奇数年）</li>
                        <li>大学生创业计划竞赛（偶数年）</li>
                        <li><strong>2025年：</strong>举办科技作品竞赛</li>
                        <li><strong>类别：</strong>科技/创业</li>
                        <li><strong>难度：</strong>高</li>
                        <li>科技创新与创业计划的最具权威性赛事之一</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 按月份分类的竞赛时间线 -->
        <h3 class="title-section" style="font-size: 16pt; margin-top: 10mm;">按月份分类的竞赛时间线</h3>

        <div class="timeline">
            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date"><i class="fas fa-calendar-alt"></i> 3月竞赛</div>
                    <div class="timeline-title">春季重要竞赛</div>
                    <div class="timeline-description">
                        <strong>全国大学生数学竞赛</strong> - 数学类顶级竞赛，通常在3月进行决赛<br>
                        <strong>全国大学生英语竞赛</strong> - 英语能力权威认证，初赛通常在4月
                    </div>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date"><i class="fas fa-calendar-alt"></i> 5月竞赛</div>
                    <div class="timeline-title">专业技能竞赛</div>
                    <div class="timeline-description">
                        <strong>周培源大学生力学竞赛</strong> - 工程类专业顶级赛事<br>
                        <strong>互联网+创新创业大赛</strong> - 校级选拔阶段开始
                    </div>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date"><i class="fas fa-calendar-alt"></i> 8月竞赛</div>
                    <div class="timeline-title">暑期重点竞赛</div>
                    <div class="timeline-description">
                        <strong>全国大学生电子设计竞赛</strong> - 电子类权威赛事，通常在8月上旬<br>
                        <strong>互联网+大学生创新创业大赛</strong> - 国家级创新创业赛事
                    </div>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="timeline-date"><i class="fas fa-calendar-alt"></i> 9月竞赛</div>
                    <div class="timeline-title">秋季核心竞赛</div>
                    <div class="timeline-description">
                        <strong>全国大学生数学建模竞赛</strong> - 数学建模权威赛事，通常在9月上旬<br>
                        <strong>"挑战杯"中国大学生创业计划竞赛</strong> - 创业计划权威赛事
                    </div>
                </div>
            </div>
        </div>

        <div class="page-footer">
            <span><i class="fas fa-trophy"></i> 竞赛规划指南</span>
            <span>第 14 页</span>
        </div>
    </section>

    <!-- Section 14: 学术发展与升学规划 -->
    <section class="page-section">
         

        <h2 class="title-section">学术发展与升学规划</h2>

        <!-- 保研规划 -->
        <div class="timeline-item">
            <div class="timeline-content">
                <div class="timeline-date"><i class="fas fa-graduation-cap"></i> 保研规划详细指南</div>
                <div class="timeline-title">推免研究生申请流程</div>
                <div class="timeline-description">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-content">
                                <div class="timeline-date"><i class="fas fa-search"></i> 大三上学期（1-3月）</div>
                                <div class="timeline-title">搜集信息，确定目标</div>
                                <div class="timeline-description">
                                    • 了解目标院校保研政策<br>
                                    • 确定专业方向和导师<br>
                                    • 评估自身保研资格
                                </div>
                            </div>
                        </div>

                        <div class="timeline-item">
                            <div class="timeline-content">
                                <div class="timeline-date"><i class="fas fa-file-alt"></i> 大三下学期（3-5月）</div>
                                <div class="timeline-title">准备夏令营申请材料</div>
                                <div class="timeline-description">
                                    • 完善个人简历和成绩单<br>
                                    • 准备推荐信和个人陈述<br>
                                    • 关注目标院校夏令营通知
                                </div>
                            </div>
                        </div>

                        <div class="timeline-item">
                            <div class="timeline-content">
                                <div class="timeline-date"><i class="fas fa-campground"></i> 大三暑期（6-8月）</div>
                                <div class="timeline-title">参加高校夏令营</div>
                                <div class="timeline-description">
                                    • 积极参加目标院校夏令营<br>
                                    • 争取优秀营员资格<br>
                                    • 与导师建立联系
                                </div>
                            </div>
                        </div>

                        <div class="timeline-item">
                            <div class="timeline-content">
                                <div class="timeline-date"><i class="fas fa-clipboard-check"></i> 大四上学期（9-10月）</div>
                                <div class="timeline-title">正式推免申请</div>
                                <div class="timeline-description">
                                    • 参加各校预推免报名和考核<br>
                                    • 在推免服务系统进行正式报名<br>
                                    • 确认录取结果
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-grid" style="margin-top: 6mm;">
                        <div class="card" style="border-left-color: #3b82f6;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <h3 class="card-title" style="color: #1e40af;">保研基本要求</h3>
                            </div>
                            <ul>
                                <li>学习成绩排名：原"双一流"高校≈前15%，普通本科≈前10%，其他≈前5%（以当年校内文件为准）</li>
                                <li>无挂科记录</li>
                                <li>英语四六级成绩优秀</li>
                                <li>有科研经历或竞赛获奖</li>
                                <li>有学生工作或社会实践经历</li>
                            </ul>
                        </div>

                        <div class="card" style="border-left-color: #10b981;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669); color: white;">
                                    <i class="fas fa-list-ol"></i>
                                </div>
                                <h3 class="card-title" style="color: #065f46;">准备步骤</h3>
                            </div>
                            <ul>
                                <li>大一大二：保持优异成绩，积极参加竞赛</li>
                                <li>大二大三：参与科研项目，发表论文</li>
                                <li>大三上：准备夏令营申请材料</li>
                                <li>大三下：参加夏令营，联系导师</li>
                                <li>大四上：确认推免资格，完成申请</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="page-footer">
            <span><i class="fas fa-graduation-cap"></i> 学术发展规划</span>
            <span>第 15 页</span>
        </div>
    </section>

    <!-- Section 15: 考研与职业规划 -->
    <section class="page-section">
         

        <h2 class="title-section">考研与职业规划</h2>

        <!-- 考研规划 -->
        <div class="timeline-item">
            <div class="timeline-content">
                <div class="timeline-date"><i class="fas fa-book-open"></i> 考研规划指南</div>
                <div class="timeline-title">研究生入学考试准备</div>
                <div class="timeline-description">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-content">
                                <div class="timeline-date"><i class="fas fa-book-open"></i> 大三学年（当前-6月）</div>
                                <div class="timeline-title">确定考研目标，开始基础复习</div>
                                <div class="timeline-description">
                                    • 确定目标院校和专业<br>
                                    • 复习英语、数学基础<br>
                                    • 开始第一轮专业课复习
                                </div>
                            </div>
                        </div>

                        <div class="timeline-item">
                            <div class="timeline-content">
                                <div class="timeline-date"><i class="fas fa-dumbbell"></i> 大三暑假（7-8月）</div>
                                <div class="timeline-title">强化复习阶段</div>
                                <div class="timeline-description">
                                    • 制定强化复习计划<br>
                                    • 完成各科目的系统复习<br>
                                    • 参加暑期强化班（可选）
                                </div>
                            </div>
                        </div>

                        <div class="timeline-item">
                            <div class="timeline-content">
                                <div class="timeline-date"><i class="fas fa-edit"></i> 大四上学期（9-12月）</div>
                                <div class="timeline-title">冲刺阶段</div>
                                <div class="timeline-description">
                                    • 考研大纲发布，网上报名<br>
                                    • 开始真题演练和模拟考试<br>
                                    • 查漏补缺，参加初试
                                </div>
                            </div>
                        </div>

                        <div class="timeline-item">
                            <div class="timeline-content">
                                <div class="timeline-date"><i class="fas fa-comments"></i> 大四下学期（2-6月）</div>
                                <div class="timeline-title">复试与录取</div>
                                <div class="timeline-description">
                                    • 初试成绩公布<br>
                                    • 准备复试或联系调剂<br>
                                    • 陆续收到录取通知书
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card" style="border-left-color: #3b82f6; margin-top: 6mm;">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;">
                                <i class="fas fa-book"></i>
                            </div>
                            <h3 class="card-title" style="color: #1e40af;">考试科目</h3>
                        </div>
                        <ul>
                            <li>思想政治理论</li>
                            <li>英语一/英语二</li>
                            <li>数学一/数学二/数学三（理工类）</li>
                            <li>专业课（根据专业确定）</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 职业准备规划 -->
        <div class="timeline-item">
            <div class="timeline-content">
                <div class="timeline-date"><i class="fas fa-briefcase"></i> 职业准备与能力发展</div>
                <div class="timeline-title">核心能力发展规划</div>
                <div class="timeline-description">
                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #f59e0b;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <h3 class="card-title" style="color: #92400e;">求职时间线</h3>
                            </div>
                            <ul>
                                <li>大三暑期（7-8月）：积极寻找暑期实习</li>
                                <li>大四上学期（9-11月）：秋招黄金时期</li>
                                <li>大四下学期（3-5月）：春招高峰期</li>
                                <li>大四下学期（6月）：毕业季</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #ef4444;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white;">
                                    <i class="fas fa-flag"></i>
                                </div>
                                <h3 class="card-title" style="color: #991b1b;">入党规划</h3>
                            </div>
                            <ul>
                                <li>综测加分，有利于保研</li>
                                <li>信息优势，获得各类资讯</li>
                                <li>政治审查优势</li>
                                <li>公务员、事业单位就业优势</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #10b981;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669); color: white;">
                                    <i class="fas fa-trophy"></i>
                                </div>
                                <h3 class="card-title" style="color: #065f46;">奖学金策略</h3>
                            </div>
                            <ul>
                                <li>保持优异的学习成绩（GPA 3.5以上）</li>
                                <li>积极参加学科竞赛和科研项目</li>
                                <li>担任学生干部，积累工作经验</li>
                                <li>参与志愿服务和社会实践</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #8b5cf6;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;">
                                    <i class="fas fa-briefcase"></i>
                                </div>
                                <h3 class="card-title" style="color: #581c87;">实习规划</h3>
                            </div>
                            <ul>
                                <li>大二暑假：初级实习，了解行业</li>
                                <li>大三暑假：核心实习，积累经验</li>
                                <li>大四上学期：深度实习，冲击转正</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 总结 -->
        <div class="timeline-item">
            <div class="timeline-content">
                <div class="timeline-date"><i class="fas fa-star"></i> 总结与建议</div>
                <div class="timeline-title">四年规划关键要点</div>
                <div class="timeline-description">
                    <div style="margin: 6mm 0;">
                        <div class="card" style="border-left-color: #3b82f6;">
                            <div class="card-header">
                                <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;">
                                    <i class="fas fa-list-ol"></i>
                                </div>
                                <h3 class="card-title" style="color: #1e40af;">四年规划要点</h3>
                            </div>
                            <ul>
                                <li><strong>大一：</strong>适应大学生活，建立良好学习习惯，探索兴趣方向</li>
                                <li><strong>大二：</strong>深入专业学习，参与科研竞赛，拓展能力边界</li>
                                <li><strong>大三：</strong>明确发展方向，强化核心能力，准备升学就业</li>
                                <li><strong>大四：</strong>冲刺目标实现，完成学业要求，顺利步入下一阶段</li>
                            </ul>
                        </div>
                    </div>

				<div style="margin: 6mm 0;">
				    <div class="card" style="border-left-color: #10b981;">
				        <div class="card-header">
				            <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669); color: white;">
				                <i class="fas fa-key"></i>
				            </div>
				            <h3 class="card-title" style="color: #065f46;">成功关键因素</h3>
				        </div>
				        <div style="margin-top: 3mm;">
				            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4mm;">
				                <!-- 左列 -->
				                <div>
				                    <div style="text-align: center; padding: 3mm; margin-bottom: 3mm;">
				                        <i class="fas fa-bullseye" style="font-size: 16pt; color: #3b82f6; margin-bottom: 2mm;"></i>
				                        <h5 style="color: #1e40af; margin-bottom: 1mm;">目标明确</h5>
				                        <p style="font-size: 9pt; color: #6b7280;">每个阶段都要有清晰的目标和可行的计划</p>
				                    </div>
				                    <div style="text-align: center; padding: 3mm;">
				                        <i class="fas fa-dumbbell" style="font-size: 16pt; color: #10b981; margin-bottom: 2mm;"></i>
				                        <h5 style="color: #065f46; margin-bottom: 1mm;">持续努力</h5>
				                        <p style="font-size: 9pt; color: #6b7280;">保持学习热情，持续提升个人能力</p>
				                    </div>
				                </div>
				                
				                <!-- 右列 -->
				                <div>
				                    <div style="text-align: center; padding: 3mm; margin-bottom: 3mm;">
				                        <i class="fas fa-users" style="font-size: 16pt; color: #f59e0b; margin-bottom: 2mm;"></i>
				                        <h5 style="color: #92400e; margin-bottom: 1mm;">积极参与</h5>
				                        <p style="font-size: 9pt; color: #6b7280;">主动参与各类活动，扩展人脉关系</p>
				                    </div>
				                    <div style="text-align: center; padding: 3mm;">
				                        <i class="fas fa-chart-line" style="font-size: 16pt; color: #ef4444; margin-bottom: 2mm;"></i>
				                        <h5 style="color: #991b1b; margin-bottom: 1mm;">及时调整</h5>
				                        <p style="font-size: 9pt; color: #6b7280;">根据实际情况灵活调整规划方案</p>
				                    </div>
				                </div>
				            </div>
				        </div>
				    </div>
				</div>

                </div>
            </div>
        </div>

        <div class="page-footer">
            <span><i class="fas fa-briefcase"></i> 职业规划与总结</span>
            <span>第 16 页</span>
        </div>
    </section>

    <!-- Section 16: 结语与祝福 -->
    <section class="page-section">
         

        <div style="text-align: center; margin-top: 30mm;">
            <i class="fas fa-graduation-cap" style="font-size: 36pt; color: #3b82f6; margin-bottom: 10mm;"></i>
            <h2 class="title-sub">开启美好大学四年</h2>
            <div style="width: 60mm; height: 3pt; background: linear-gradient(90deg, #f97316, #fb923c); margin: 0 auto 15mm; border-radius: 2pt;"></div>
        </div>
        
        <div class="card-grid">
            <div class="card" style="border-left-color: #3b82f6;">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-flag-checkered"></i>
                    </div>
                    <h3 class="card-title">新起点，新征程</h3>
                </div>
                <p style="font-size: 11pt; color: #374151; line-height: 1.6;">
                    大学生活是人生的新起点，充分的准备是适应大学生活的关键。从高中到大学，不仅是环境的改变，更是学习方式、生活方式和思维方式的全面转变。
                </p>
            </div>
            
            <div class="card" style="border-left-color: #f59e0b;">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #fef3c7, #fed7aa); color: #92400e;">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3 class="card-title" style="color: #92400e;">保持好奇心</h3>
                </div>
                <p style="font-size: 11pt; color: #374151; line-height: 1.6;">
                    保持对知识的渴望和探索精神，勇于尝试新事物，敢于挑战自我。大学是发现自我、塑造自我的黄金时期，珍惜这段宝贵的时光。
                </p>
            </div>
        </div>
        
        <div class="card-grid">
            <div class="card" style="background: linear-gradient(135deg, #eff6ff, #dbeafe); border-left-color: #3b82f6; text-align: center;">
                <i class="fas fa-seedling" style="font-size: 20pt; color: #3b82f6; margin-bottom: 4mm;"></i>
                <h4 style="font-size: 14pt; font-weight: 700; color: #1e40af; margin-bottom: 3mm;">培养良好习惯</h4>
                <p style="font-size: 10pt; color: #3b82f6;">自律是成功的基础，建立规律作息和高效学习习惯</p>
            </div>

            <div class="card" style="background: linear-gradient(135deg, #fef3c7, #fed7aa); border-left-color: #f59e0b; text-align: center;">
                <i class="fas fa-brain" style="font-size: 20pt; color: #f59e0b; margin-bottom: 4mm;"></i>
                <h4 style="font-size: 14pt; font-weight: 700; color: #92400e; margin-bottom: 3mm;">独立思考</h4>
                <p style="font-size: 10pt; color: #f59e0b;">培养批判性思维，不盲从权威，形成自己的观点</p>
            </div>
        </div>

        <div style="margin-top: 6mm;">
            <div class="card" style="background: linear-gradient(135deg, #d1fae5, #a7f3d0); border-left-color: #10b981; text-align: center;">
                <i class="fas fa-users" style="font-size: 20pt; color: #10b981; margin-bottom: 4mm;"></i>
                <h4 style="font-size: 14pt; font-weight: 700; color: #065f46; margin-bottom: 3mm;">积极参与</h4>
                <p style="font-size: 10pt; color: #10b981;">加入社团组织，参与志愿活动，拓展人脉与视野</p>
            </div>
        </div>
        
        <div class="info-box" style="background: linear-gradient(135deg, #eff6ff, #fef3c7); text-align: center;">
            <div class="info-box-title" style="font-size: 16pt; color: #1e40af;">祝福与期望</div>
            <div class="info-box-content" style="font-size: 12pt; color: #1e40af;">
                建议结合自身实际，灵活参考本手册，在大学四年中不断调整与成长。<br>
                愿每位新生都能在知识的海洋中尽情遨游，在青春的舞台上绽放光彩！
            </div>
        </div>
        
        <div class="page-footer">
            <span><i class="fas fa-heart"></i> 祝您大学生活愉快</span>
            <span>第 17 页</span>
        </div>
    </section>

    <script>
/*         // 增强的脚本功能
        function printDocument() {
            // 打印前的准备工作
            showLoadingIndicator('正在准备打印...');

            setTimeout(() => {
                window.print();
                hideLoadingIndicator();
            }, 500);
        }

        // 显示加载指示器
        function showLoadingIndicator(message) {
            const indicator = document.createElement('div');
            indicator.id = 'loadingIndicator';
            indicator.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                           background: rgba(0,0,0,0.5); z-index: 9999; display: flex;
                           align-items: center; justify-content: center;">
                    <div style="background: white; padding: 20px; border-radius: 8px;
                               box-shadow: 0 4px 12px rgba(0,0,0,0.3);">
                        <i class="fas fa-spinner fa-spin" style="margin-right: 10px;"></i>
                        ${message}
                    </div>
                </div>
            `;
            document.body.appendChild(indicator);
        }

        // 隐藏加载指示器
        function hideLoadingIndicator() {
            const indicator = document.getElementById('loadingIndicator');
            if (indicator) {
                indicator.remove();
            }
        }

        // 为工具栏按钮添加事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            const printBtn = document.querySelector('.toolbar button');
            if (printBtn) {
                printBtn.addEventListener('click', printDocument);
            }

            // 初始化就业信息图表
            try {
                initEmploymentChart();
            } catch (error) {
                console.error('图表初始化失败:', error);
                // 显示友好的错误信息
                const chartDom = document.getElementById('employmentChart');
                if (chartDom) {
                    chartDom.innerHTML = '<div style="text-align: center; padding: 20px; color: #6b7280;">图表加载失败，请刷新页面重试</div>';
                }
            }

            // 添加页面加载完成提示
            showPageLoadedNotification();
        });

        // 页面加载完成提示
        function showPageLoadedNotification() {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px;
                background: #10b981; color: white;
                padding: 10px 20px; border-radius: 6px;
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
                z-index: 1000; opacity: 0; transition: opacity 0.3s ease;
            `;
            notification.innerHTML = '<i class="fas fa-check"></i> 手册加载完成';
            document.body.appendChild(notification);

            setTimeout(() => notification.style.opacity = '1', 100);
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => notification.remove(), 300);
            }, 2000);
        } */

        
        // 为工具栏按钮添加事件监听器
        document.addEventListener('DOMContentLoaded', function() {
        	
            // 初始化就业信息图表
            try {
                initEmploymentChart();
            } catch (error) {
                console.error('图表初始化失败:', error);
                // 如果图表初始化失败，隐藏整个卡片
                hideEmploymentChart();
            }
        });
        
        
        // 初始化就业信息图表 - 增强版
        function initEmploymentChart() {
            const chartDom = document.getElementById('employmentChart');
            const chartCard = document.getElementById('employmentChartCard');

            // 如果图表容器不存在，说明没有数据，直接返回
            if (!chartDom) {
                console.log('就业图表容器不存在，可能是因为没有就业数据');
                return;
            }

            try {
                // 检查ECharts是否加载
                if (typeof echarts === 'undefined') {
                    console.error('ECharts库未加载');
                    hideEmploymentChart();
                    return;
                }

                const myChart = echarts.init(chartDom);

                <%if (universityInfo != null) { %>
                // 准备数据 - 增加更多维度
                const employmentData = [
                    {
                        name: '就业公司数量',
                        value: <%=universityInfo.getCnt_company() > 0 ? universityInfo.getCnt_company() : 0%>,
                        unit: '家',
                        color: '#3b82f6'
                    },
                    {
                        name: '就业人数',
                        value: <%=universityInfo.getCnt_employ() > 0 ? universityInfo.getCnt_employ() : 0%>,
                        unit: '人',
                        color: '#ef4444'
                    }
                ];

                // 验证数据有效性
                const totalCompanies = employmentData[0].value;
                const totalEmployees = employmentData[1].value;

                // 如果所有数据都为0，隐藏图表
                if (totalCompanies === 0 && totalEmployees === 0) {
                    console.log('就业数据为空，隐藏图表');
                    hideEmploymentChart();
                    return;
                }

                const avgEmployeesPerCompany = totalCompanies > 0 ? Math.round(totalEmployees / totalCompanies) : 0;

            // 配置图表选项
            const option = {
                title: {
                    text: '<%=Tools.view(studentCard.getC_university())%> 就业数据统计',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'bold',
                        color: '#1e40af'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        return params.name + ': ' + params.value + params.data.unit;
                    }
                },
                legend: {
                    orient: 'horizontal',
                    bottom: '10%',
                    textStyle: {
                        fontSize: 12
                    }
                },
                series: [
                    {
                        name: '就业信息',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        center: ['50%', '45%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: true,
                            position: 'outside',
                            formatter: function(params) {
                                return params.name + '\n' + params.value + params.data.unit;
                            },
                            fontSize: 11,
                            fontWeight: 'bold'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 14,
                                fontWeight: 'bold'
                            },
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        labelLine: {
                            show: true
                        },
                        data: employmentData.map(item => ({
                            value: item.value,
                            name: item.name,
                            unit: item.unit,
                            itemStyle: {
                                color: item.color
                            }
                        }))
                    }
                ]
            };

            // 设置图表配置
            myChart.setOption(option);

            // 响应式处理
            window.addEventListener('resize', function() {
                myChart.resize();
            });
            <%} else { %>
            // 如果没有学校信息，隐藏图表
            console.log('没有学校信息，隐藏就业图表');
            hideEmploymentChart();
            <%} %>

            } catch (error) {
                console.error('图表渲染失败:', error);
                hideEmploymentChart();
            }
        }

        // 隐藏就业图表的函数
        function hideEmploymentChart() {
            const chartCard = document.getElementById('employmentChartCard');
            if (chartCard) {
                chartCard.style.display = 'none';
                console.log('就业数据可视化卡片已隐藏');
            }
        }

        // 平滑滚动到顶部
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 添加返回顶部按钮
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const backToTopBtn = document.getElementById('backToTop');

            if (!backToTopBtn) {
                const btn = document.createElement('button');
                btn.id = 'backToTop';
                btn.innerHTML = '<i class="fas fa-arrow-up"></i>';
                btn.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    left: 20px;
                    width: 50px;
                    height: 50px;
                    border-radius: 50%;
                    background: linear-gradient(135deg, #f59e0b, #d97706);
                    color: white;
                    border: none;
                    cursor: pointer;
                    font-size: 18px;
                    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
                    transition: all 0.3s ease;
                    z-index: 1000;
                    display: none;
                `;
                btn.onclick = scrollToTop;
                document.body.appendChild(btn);
            }

            const backToTopBtn2 = document.getElementById('backToTop');
            if (scrollTop > 300) {
                backToTopBtn2.style.display = 'block';
            } else {
                backToTopBtn2.style.display = 'none';
            }
        });

        // 目录导航平滑滚动
        document.addEventListener('DOMContentLoaded', function() {
            const tocLinks = document.querySelectorAll('.planning-handbook-container .toc-item a');
            tocLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });

        // 打印优化
        window.addEventListener('beforeprint', function() {
            document.body.style.background = 'white';
        });

        window.addEventListener('afterprint', function() {
            document.body.style.background = '';
        });
    </script>
</div> <!-- End of planning-handbook-container -->
<%if (isStandalone) {%>
</body>
</html>
<%}%>

