package com.daxue.db;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;

import com.daxue.db.connection.DatabaseUtils;
import com.daxue.utils.SQLLogUtils;
import com.daxue.utils.Tools;

public class DaxueJdbc {
	
	public static int PAGE_SIZE = 20;

	public DxAgent getDxAgent(String agent_id) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM dx_agent WHERE agent_id = ?";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, agent_id);
			DxAgent bean = null;
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(" === getDxAgent : ", ps); 
			while (rs.next()) {
				bean = new DxAgent();
				bean.setAgent_id(rs.getString("agent_id"));
				bean.setA_passwd(rs.getString("a_passwd"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setActive_tm(rs.getTimestamp("active_tm"));
				bean.setLast_login_tm(rs.getTimestamp("last_login_tm"));
				bean.setExpire_tm(rs.getTimestamp("expire_tm"));
				bean.setRemark(rs.getString("remark"));
				bean.setStatus(rs.getInt("status"));
				bean.setRpt_point_cnt(rs.getInt("rpt_point_cnt"));
				return bean;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			DatabaseUtils.closeAllResources(rs, ps, conn);
		}
		return null;
	}
	
	public DxCard getDxCardByCid(String c_id) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	        conn = DatabaseUtils.getConnection();
	        String SQL = "SELECT * FROM dx_card WHERE c_id = ? and status = 1";
	        ps = conn.prepareStatement(SQL);
	        ps.setString(1, c_id);
	        DxCard bean = null;
	        rs = ps.executeQuery();
	        SQLLogUtils.printSQL(" === getDxCard : ", ps);
	        while (rs.next()) {
	            bean = new DxCard();
	            bean.setC_id(rs.getString("c_id"));
	            bean.setId(rs.getString("id"));
	            bean.setC_passwd(rs.getString("c_passwd"));
	            bean.setC_prov(rs.getString("c_prov"));
	            bean.setC_city(rs.getString("c_city"));
	            bean.setC_name(rs.getString("c_name"));
	            bean.setC_university(rs.getString("c_university"));
	            bean.setC_major(rs.getString("c_major"));
	            bean.setCreate_tm(rs.getTimestamp("create_tm"));
	            bean.setActive_tm(rs.getTimestamp("active_tm"));
	            bean.setLast_login_tm(rs.getTimestamp("last_login_tm"));
	            bean.setExpire_tm(rs.getTimestamp("expire_tm"));
	            bean.setRemark(rs.getString("remark"));
	            bean.setC_phone(rs.getString("c_phone"));
	            bean.setAgent_id(rs.getString("agent_id"));
	            bean.setStatus(rs.getInt("status"));
	            return bean;
	        }
	    } catch (Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        DatabaseUtils.closeAllResources(rs, ps, conn);
	    }
	    return null;
	}
	
	/**
	 * 根据ID查询DxCard
	 * @param id
	 * @return
	 */
	public DxCard getDxCardById(String id) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    try {
	        conn = DatabaseUtils.getConnection();
	        String SQL = "SELECT * FROM dx_card WHERE id = ? and status = 1";
	        ps = conn.prepareStatement(SQL);
	        ps.setString(1, id);
	        DxCard bean = null;
	        rs = ps.executeQuery();
	        SQLLogUtils.printSQL(" === getDxCard : ", ps);
	        while (rs.next()) {
	            bean = new DxCard();
	            bean.setC_id(rs.getString("c_id"));
	            bean.setId(rs.getString("id"));
	            bean.setC_passwd(rs.getString("c_passwd"));
	            bean.setC_prov(rs.getString("c_prov"));
	            bean.setC_city(rs.getString("c_city"));
	            bean.setC_name(rs.getString("c_name"));
	            bean.setC_university(rs.getString("c_university"));
	            bean.setC_major(rs.getString("c_major"));
	            bean.setCreate_tm(rs.getTimestamp("create_tm"));
	            bean.setActive_tm(rs.getTimestamp("active_tm"));
	            bean.setLast_login_tm(rs.getTimestamp("last_login_tm"));
	            bean.setExpire_tm(rs.getTimestamp("expire_tm"));
	            bean.setRemark(rs.getString("remark"));
	            bean.setC_phone(rs.getString("c_phone"));
	            bean.setAgent_id(rs.getString("agent_id"));
	            bean.setStatus(rs.getInt("status"));
	            return bean;
	        }
	    } catch (Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        DatabaseUtils.closeAllResources(rs, ps, conn);
	    }
	    return null;
	}
	
	/**
	 * 根据院校名称，查询出该院校的所有招生计划
	 * @param yxmc
	 * @param pageNumber
	 * @return
	 */
	public List<ZyzdMasterPlan> listZyzdMasterPlanByYxmc(String yxmc) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<ZyzdMasterPlan> list = new ArrayList<>();
	    try {
	        conn = DatabaseUtils.getConnection();

	        // 基础条件
	        String sqlWhere = "FROM zyzd_master_plan_2025 WHERE yxmc LIKE ?";

	        // 查询分页数据
	        String sqlData = "SELECT * " + sqlWhere + " ORDER BY id ";
	        ps = conn.prepareStatement(sqlData);
	        ps.setString(1, yxmc);
	        rs = ps.executeQuery();
	        SQLLogUtils.printSQL("=== listZyzdMasterPlan2025ByYxmc : ", ps);

	        while (rs.next()) {
	        	ZyzdMasterPlan bean = new ZyzdMasterPlan();
	        	bean.setId(rs.getInt("id"));
	        	bean.setYxsf(rs.getString("yxsf"));
	        	bean.setYxcs(rs.getString("yxcs"));
	        	bean.setYxdm(rs.getString("yxdm"));
	        	bean.setYxmc(rs.getString("yxmc"));
	        	bean.setXymc(rs.getString("xymc"));
	        	bean.setXydm(rs.getString("xydm"));
	        	bean.setZymc(rs.getString("zymc"));
	        	bean.setZydm(rs.getString("zydm"));
	        	bean.setYjfx(rs.getString("yjfx"));
	        	bean.setYjfxdm(rs.getString("yjfxdm"));
	        	bean.setXxfs(rs.getString("xxfs"));
	        	bean.setZsrs(rs.getString("zsrs"));
	        	bean.setZz(rs.getString("zz"));
	        	bean.setYy(rs.getString("yy"));
	        	bean.setZy1(rs.getString("zy1"));
	        	bean.setZy2(rs.getString("zy2"));
	        	bean.setYxlx(rs.getString("yxlx"));
	        	bean.setSbjh(rs.getString("sbjh"));
	            list.add(bean);
	        }

	    } catch (Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        DatabaseUtils.closeAllResources(rs, ps, conn);
	    }

	    return list;
	}
	
	/**
	 * 根据专业名称，查询出该院校的所有招生计划
	 * @param zymc
	 * @param pageNumber
	 * @return
	 */
	public List<ZyzdMasterPlan> listZyzdMasterPlanByZymc(String zymc) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<ZyzdMasterPlan> list = new ArrayList<>();
	    try {
	        conn = DatabaseUtils.getConnection();

	        // 基础条件
	        String sqlWhere = "FROM zyzd_master_plan_2025 WHERE zymc LIKE ?";

	        // 查询分页数据
	        String sqlData = "SELECT * " + sqlWhere + " ORDER BY id ";
	        ps = conn.prepareStatement(sqlData);
	        ps.setString(1, zymc);
	        rs = ps.executeQuery();
	        SQLLogUtils.printSQL("=== listZyzdMasterPlan2025ByYxmc : ", ps);

	        while (rs.next()) {
	        	ZyzdMasterPlan bean = new ZyzdMasterPlan();
	        	bean.setId(rs.getInt("id"));
	        	bean.setYxsf(rs.getString("yxsf"));
	        	bean.setYxcs(rs.getString("yxcs"));
	        	bean.setYxdm(rs.getString("yxdm"));
	        	bean.setYxmc(rs.getString("yxmc"));
	        	bean.setXymc(rs.getString("xymc"));
	        	bean.setXydm(rs.getString("xydm"));
	        	bean.setZymc(rs.getString("zymc"));
	        	bean.setZydm(rs.getString("zydm"));
	        	bean.setYjfx(rs.getString("yjfx"));
	        	bean.setYjfxdm(rs.getString("yjfxdm"));
	        	bean.setXxfs(rs.getString("xxfs"));
	        	bean.setZsrs(rs.getString("zsrs"));
	        	bean.setZz(rs.getString("zz"));
	        	bean.setYy(rs.getString("yy"));
	        	bean.setZy1(rs.getString("zy1"));
	        	bean.setZy2(rs.getString("zy2"));
	        	bean.setYxlx(rs.getString("yxlx"));
	        	bean.setSbjh(rs.getString("sbjh"));
	            list.add(bean);
	        }

	    } catch (Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        DatabaseUtils.closeAllResources(rs, ps, conn);
	    }

	    return list;
	}
	
	/**
	 * 根据院校名称，查询出该院校所有年份的专业录取数据（按年份倒序）
	 * @param yxmc 院校名称
	 * @return 排序后的列表
	 */
	public List<CareerUniversityMajorData> listCareerUniversityMajorDataByYxmc(String yxmc) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<CareerUniversityMajorData> list = new ArrayList<>();

	    try {
	        conn = DatabaseUtils.getConnection();

	        String sql = "SELECT * FROM career_university_major_data WHERE school_name LIKE ? ORDER BY data_year DESC";
	        ps = conn.prepareStatement(sql);
	        ps.setString(1, "%" + yxmc + "%");
	        rs = ps.executeQuery();
	        
	        SQLLogUtils.printSQL("=== listCareerUniversityMajorDataByYxmc : ", ps);

	        while (rs.next()) {
	            CareerUniversityMajorData bean = new CareerUniversityMajorData();
	            bean.setSchool_id(rs.getInt("school_id"));
	            bean.setSchool_name(rs.getString("school_name"));
	            bean.setData_year(rs.getInt("data_year"));
	            bean.setDegree_type(rs.getInt("degree_type"));
	            bean.setDegree_type_name(rs.getString("degree_type_name"));
	            bean.setSpecial_code(rs.getString("special_code"));
	            bean.setSpecial_name(rs.getString("special_name"));
	            bean.setTotal(rs.getInt("total"));
	            bean.setPolitics(rs.getInt("politics"));
	            bean.setEnglish(rs.getInt("english"));
	            bean.setSpecial_one(rs.getInt("special_one"));
	            bean.setSpecial_two(rs.getInt("special_two"));
	            bean.setNote(rs.getString("note"));
	            bean.setDiff_total(rs.getInt("diff_total"));
	            bean.setDiff_politics(rs.getInt("diff_politics"));
	            bean.setDiff_english(rs.getInt("diff_english"));
	            bean.setDiff_special_one(rs.getInt("diff_special_one"));
	            bean.setDiff_special_two(rs.getInt("diff_special_two"));
	            list.add(bean);
	        }
	    } catch (Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        DatabaseUtils.closeAllResources(rs, ps, conn);
	    }

	    return list;
	}
	
	/**
	 * 根据专业名称，查询所有院校该专业的录取数据（按年份倒序）
	 * @param zymc 专业名称
	 * @return 排序后的列表
	 */
	public List<CareerUniversityMajorData> listCareerUniversityMajorDataByZymc(String zymc) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<CareerUniversityMajorData> list = new ArrayList<>();

	    try {
	        conn = DatabaseUtils.getConnection();

	        String sql = "SELECT * FROM career_university_major_data WHERE special_name LIKE ? ORDER BY data_year DESC";
	        ps = conn.prepareStatement(sql);
	        ps.setString(1, "%" + zymc + "%");
	        rs = ps.executeQuery();
	        
	        SQLLogUtils.printSQL("=== listCareerUniversityMajorDataByZymc : ", ps);

	        while (rs.next()) {
	            CareerUniversityMajorData bean = new CareerUniversityMajorData();
	            bean.setSchool_id(rs.getInt("school_id"));
	            bean.setSchool_name(rs.getString("school_name"));
	            bean.setData_year(rs.getInt("data_year"));
	            bean.setDegree_type(rs.getInt("degree_type"));
	            bean.setDegree_type_name(rs.getString("degree_type_name"));
	            bean.setSpecial_code(rs.getString("special_code"));
	            bean.setSpecial_name(rs.getString("special_name"));
	            bean.setTotal(rs.getInt("total"));
	            bean.setPolitics(rs.getInt("politics"));
	            bean.setEnglish(rs.getInt("english"));
	            bean.setSpecial_one(rs.getInt("special_one"));
	            bean.setSpecial_two(rs.getInt("special_two"));
	            bean.setNote(rs.getString("note"));
	            bean.setDiff_total(rs.getInt("diff_total"));
	            bean.setDiff_politics(rs.getInt("diff_politics"));
	            bean.setDiff_english(rs.getInt("diff_english"));
	            bean.setDiff_special_one(rs.getInt("diff_special_one"));
	            bean.setDiff_special_two(rs.getInt("diff_special_two"));
	            list.add(bean);
	        }
	    } catch (Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        DatabaseUtils.closeAllResources(rs, ps, conn);
	    }

	    return list;
	}
	
	/**
	 * 根据专业名称+年份，查询所有院校该专业某年的录取数据（按考研分数倒序）
	 * @param zymc 专业名称
	 * @return 排序后的列表
	 */
	public List<CareerUniversityMajorData> listCareerUniversityMajorDataByZymc(int nf, String zymc) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<CareerUniversityMajorData> list = new ArrayList<>();

	    try {
	        conn = DatabaseUtils.getConnection();

	        String sql = "SELECT * FROM career_university_major_data WHERE special_name LIKE ? and data_year = ? ORDER BY total DESC";
	        ps = conn.prepareStatement(sql);
	        ps.setString(1, "%" + zymc + "%");
	        ps.setInt(2, nf);
	        rs = ps.executeQuery();
	        
	        SQLLogUtils.printSQL("=== listCareerUniversityMajorDataByZymc : ", ps);

	        while (rs.next()) {
	            CareerUniversityMajorData bean = new CareerUniversityMajorData();
	            bean.setSchool_id(rs.getInt("school_id"));
	            bean.setSchool_name(rs.getString("school_name"));
	            bean.setData_year(rs.getInt("data_year"));
	            bean.setDegree_type(rs.getInt("degree_type"));
	            bean.setDegree_type_name(rs.getString("degree_type_name"));
	            bean.setSpecial_code(rs.getString("special_code"));
	            bean.setSpecial_name(rs.getString("special_name"));
	            bean.setTotal(rs.getInt("total"));
	            bean.setPolitics(rs.getInt("politics"));
	            bean.setEnglish(rs.getInt("english"));
	            bean.setSpecial_one(rs.getInt("special_one"));
	            bean.setSpecial_two(rs.getInt("special_two"));
	            bean.setNote(rs.getString("note"));
	            bean.setDiff_total(rs.getInt("diff_total"));
	            bean.setDiff_politics(rs.getInt("diff_politics"));
	            bean.setDiff_english(rs.getInt("diff_english"));
	            bean.setDiff_special_one(rs.getInt("diff_special_one"));
	            bean.setDiff_special_two(rs.getInt("diff_special_two"));
	            list.add(bean);
	        }
	    } catch (Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        DatabaseUtils.closeAllResources(rs, ps, conn);
	    }

	    return list;
	}
	
	/**
	 * 根据专业名称集合+年份，查询所有院校该专业集合某年的录取数据（按考研分数倒序）
	 * @param zymc 专业名称
	 * @return 排序后的列表
	 */
	public List<CareerUniversityMajorData> listCareerUniversityMajorDataByZymc(int nf, HashSet<String> zymcSets) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<CareerUniversityMajorData> list = new ArrayList<>();

	    try {
	        conn = DatabaseUtils.getConnection();

	        String sql = "SELECT * FROM career_university_major_data WHERE special_name in ("+Tools.getSQLQueryin(zymcSets)+") and data_year = ? ORDER BY total DESC";
	        ps = conn.prepareStatement(sql);
	        ps.setInt(1, nf);
	        rs = ps.executeQuery();
	        
	        SQLLogUtils.printSQL("=== listCareerUniversityMajorDataByZymc : ", ps);

	        while (rs.next()) {
	            CareerUniversityMajorData bean = new CareerUniversityMajorData();
	            bean.setSchool_id(rs.getInt("school_id"));
	            bean.setSchool_name(rs.getString("school_name"));
	            bean.setData_year(rs.getInt("data_year"));
	            bean.setDegree_type(rs.getInt("degree_type"));
	            bean.setDegree_type_name(rs.getString("degree_type_name"));
	            bean.setSpecial_code(rs.getString("special_code"));
	            bean.setSpecial_name(rs.getString("special_name"));
	            bean.setTotal(rs.getInt("total"));
	            bean.setPolitics(rs.getInt("politics"));
	            bean.setEnglish(rs.getInt("english"));
	            bean.setSpecial_one(rs.getInt("special_one"));
	            bean.setSpecial_two(rs.getInt("special_two"));
	            bean.setNote(rs.getString("note"));
	            bean.setDiff_total(rs.getInt("diff_total"));
	            bean.setDiff_politics(rs.getInt("diff_politics"));
	            bean.setDiff_english(rs.getInt("diff_english"));
	            bean.setDiff_special_one(rs.getInt("diff_special_one"));
	            bean.setDiff_special_two(rs.getInt("diff_special_two"));
	            list.add(bean);
	        }
	    } catch (Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        DatabaseUtils.closeAllResources(rs, ps, conn);
	    }

	    return list;
	}
	
	
	public ResultVO listDxCardByAgentId(String agent_id, int pageNumber) {
		ResultVO resultVO = new ResultVO();
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<DxCard> list = new ArrayList<>();
	    int recordCnt = 0;
	    try {
	        conn = DatabaseUtils.getConnection();
	        String SQL = "FROM dx_card WHERE agent_id = ? and status = 1 ";
	        ps = conn.prepareStatement("SELECT * " + SQL + " order by create_tm DESC LIMIT ?,?");
	        ps.setString(1, agent_id);
	        ps.setInt(2, (pageNumber - 1) * PAGE_SIZE);
	        ps.setInt(3, PAGE_SIZE);
	        rs = ps.executeQuery();
	        SQLLogUtils.printSQL(" === listDxCardByAgentId : ", ps);
	        while (rs.next()) {
	            DxCard bean = new DxCard();
	            bean.setC_id(rs.getString("c_id"));
	            bean.setId(rs.getString("id"));
	            bean.setC_passwd(rs.getString("c_passwd"));
	            bean.setC_prov(rs.getString("c_prov"));
	            bean.setC_city(rs.getString("c_city"));
	            bean.setC_name(rs.getString("c_name"));
	            bean.setC_university(rs.getString("c_university"));
	            bean.setC_major(rs.getString("c_major"));
	            bean.setCreate_tm(rs.getTimestamp("create_tm"));
	            bean.setActive_tm(rs.getTimestamp("active_tm"));
	            bean.setLast_login_tm(rs.getTimestamp("last_login_tm"));
	            bean.setExpire_tm(rs.getTimestamp("expire_tm"));
	            bean.setRemark(rs.getString("remark"));
	            bean.setC_phone(rs.getString("c_phone"));
	            bean.setAgent_id(rs.getString("agent_id"));
	            bean.setStatus(rs.getInt("status"));
	            list.add(bean);
	        }
	        
	        rs.close();
	        ps.close();
	        
	        ps = conn.prepareStatement("SELECT count(*) as cnt " + SQL);
	        ps.setString(1, agent_id);
	        rs = ps.executeQuery();
	        while (rs.next()) {
	        	recordCnt = rs.getInt("cnt");
	        }
	    } catch (Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        DatabaseUtils.closeAllResources(rs, ps, conn);
	    }
	    resultVO.setCurrentPage(pageNumber);
	    resultVO.setRecordCnt(recordCnt);
	    resultVO.setResult(list);
	    return resultVO;
	}
	
	/**
	 * 获得代理商已经创建的学生数量(删除的不包含在内)
	 * @param agent_id
	 * @param pageNumber
	 * @return
	 */
	public int getDxCardCntByAgentId(String agent_id) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    int recordCnt = 0;
	    try {
	        conn = DatabaseUtils.getConnection();
	        String SQL = "FROM dx_card WHERE agent_id = ? and status = 1 ";
	        ps = conn.prepareStatement("SELECT count(*) as cnt " + SQL);
	        ps.setString(1, agent_id);
	        rs = ps.executeQuery();
	        while (rs.next()) {
	        	recordCnt = rs.getInt("cnt");
	        }
	    } catch (Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        DatabaseUtils.closeAllResources(rs, ps, conn);
	    }
	    return recordCnt;
	}
	
	public boolean updateDxAgent(DxAgent dxAgent) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        try {
            conn = DatabaseUtils.getConnection();
            String sql = "UPDATE dx_agent SET last_login_tm = ?, active_tm = ? WHERE agent_id = ?";
            pstmt = conn.prepareStatement(sql);

            pstmt.setTimestamp(1, new Timestamp(dxAgent.getLast_login_tm().getTime()));
            pstmt.setTimestamp(2, new Timestamp(dxAgent.getActive_tm().getTime()));
            pstmt.setString(3, dxAgent.getAgent_id());

            int affectedRows = pstmt.executeUpdate();
            
            SQLLogUtils.printSQL(" === updateDxAgent : ", pstmt); 
            
            return affectedRows > 0;
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
        	DatabaseUtils.closeAllResources(null, pstmt, conn);
        }
        return false;
    }
	
	/**
	 * 根据专业名称，获得就业统计最多的前20个行业
	 * @param zymc_org
	 * @return
	 */
	public List<JiuyeBean> getJiuyeByZy(String zymc_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JiuyeBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT group_name, COUNT(*) as ct from career_jy_all_2024 x WHERE x.zymc LIKE ? GROUP BY group_name ORDER BY COUNT(*) DESC LIMIT 20";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + zymc_org + "%");
			rs = ps.executeQuery();
			JiuyeBean bean = null;
			while (rs.next()) {
				bean = new JiuyeBean();
				bean.setCnt(rs.getInt("ct"));
				bean.setSshy(rs.getString("group_name"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			DatabaseUtils.closeAllResources(rs, ps, conn);
		}
		return list;
	}
	
	/**
	 * 根据院校名称，获得就业统计最多的前20个行业
	 * @param yxmc_org
	 * @return
	 */
	public List<JiuyeBean> getJiuyeByYx(String yxmc_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<JiuyeBean> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT group_name, COUNT(*) as ct from career_jy_all_2024 x WHERE x.yxmc LIKE ? GROUP BY group_name ORDER BY COUNT(*) DESC LIMIT 20";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + yxmc_org + "%");
			rs = ps.executeQuery();
			JiuyeBean bean = null;
			while (rs.next()) {
				bean = new JiuyeBean();
				bean.setCnt(rs.getInt("ct"));
				bean.setSshy(rs.getString("group_name"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			DatabaseUtils.closeAllResources(rs, ps, conn);
		}
		return list;
	}
	
	/**
	 * 根据专业名称，获取该校所有专业软科排行
	 * @param zymc_org
	 * @return
	 */
	public List<ZyzdMajorRanking> getRkRankinZy(String zymc_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_base_rank_rk_major WHERE zymc = ? ORDER BY sort, ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, zymc_org);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setLevel(rs.getString("ranking_level"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			DatabaseUtils.closeAllResources(rs, ps, conn);
		}
		return list;
	}
	
	/**
	 * 根据院校名称，获取该专业所有院校软科排行
	 * @param yxmc_org
	 * @return
	 */
	public List<ZyzdMajorRanking> getRkRankingYx(String yxmc_org) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdMajorRanking> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_base_rank_rk_major WHERE yxmc = ? ORDER BY sort, ranking";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmc_org);
			rs = ps.executeQuery();
			ZyzdMajorRanking bean = null;
			while (rs.next()) {
				bean = new ZyzdMajorRanking();
				bean.setRanking(rs.getInt("ranking"));
				bean.setYxmc(rs.getString("yxmc"));
				bean.setZymc(rs.getString("zymc"));
				bean.setLevel(rs.getString("ranking_level"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			DatabaseUtils.closeAllResources(rs, ps, conn);
		}
		return list;
	}
	
	/**
	 * 根据代理商ID和过滤条件查询DxCard列表
	 * @param agent_id 代理商ID
	 * @param c_name 学生姓名（模糊查询）
	 * @param c_phone 学生电话（模糊查询）
	 * @param c_university 学校名称（模糊查询）
	 * @param c_major 专业名称（模糊查询）
	 * @param pageNumber 页码
	 * @return
	 */
	public ResultVO searchDxCardsByAgentId(String agent_id, String c_name, String c_phone, String c_university, String c_major, int pageNumber) {
		ResultVO resultVO = new ResultVO();
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<DxCard> list = new ArrayList<>();
		int recordCnt = 0;
		
		try {
			conn = DatabaseUtils.getConnection();
			
			// 构建动态SQL
			StringBuilder sqlBuilder = new StringBuilder("FROM dx_card WHERE agent_id = ? and status = 1");
			List<String> params = new ArrayList<>();
			params.add(agent_id);
			
			if (!Tools.isEmpty(c_name)) {
				sqlBuilder.append(" AND c_name LIKE ?");
				params.add("%" + c_name + "%");
			}
			if (!Tools.isEmpty(c_phone)) {
				sqlBuilder.append(" AND c_phone LIKE ?");
				params.add("%" + c_phone + "%");
			}
			if (!Tools.isEmpty(c_university)) {
				sqlBuilder.append(" AND c_university LIKE ?");
				params.add("%" + c_university + "%");
			}
			if (!Tools.isEmpty(c_major)) {
				sqlBuilder.append(" AND c_major LIKE ?");
				params.add("%" + c_major + "%");
			}
			
			String baseSQL = sqlBuilder.toString();
			
			// 查询数据
			String dataSQL = "SELECT * " + baseSQL + " ORDER BY create_tm DESC LIMIT ?,?";
			ps = conn.prepareStatement(dataSQL);
			for (int i = 0; i < params.size(); i++) {
				ps.setString(i + 1, params.get(i));
			}
			ps.setInt(params.size() + 1, (pageNumber - 1) * PAGE_SIZE);
			ps.setInt(params.size() + 2, PAGE_SIZE);
			
			rs = ps.executeQuery();
			SQLLogUtils.printSQL(" === searchDxCardsByAgentId : ", ps);
			
			while (rs.next()) {
				DxCard bean = new DxCard();
				bean.setC_id(rs.getString("c_id"));
				bean.setId(rs.getString("id"));
				bean.setC_passwd(rs.getString("c_passwd"));
				bean.setC_prov(rs.getString("c_prov"));
				bean.setC_city(rs.getString("c_city"));
				bean.setC_name(rs.getString("c_name"));
				bean.setC_university(rs.getString("c_university"));
				bean.setC_major(rs.getString("c_major"));
				bean.setCreate_tm(rs.getTimestamp("create_tm"));
				bean.setActive_tm(rs.getTimestamp("active_tm"));
				bean.setLast_login_tm(rs.getTimestamp("last_login_tm"));
				bean.setExpire_tm(rs.getTimestamp("expire_tm"));
				bean.setRemark(rs.getString("remark"));
				bean.setC_phone(rs.getString("c_phone"));
				bean.setAgent_id(rs.getString("agent_id"));
				bean.setStatus(rs.getInt("status"));
				list.add(bean);
			}
			
			rs.close();
			ps.close();
			
			// 查询总数
			String countSQL = "SELECT count(*) as cnt " + baseSQL;
			ps = conn.prepareStatement(countSQL);
			for (int i = 0; i < params.size(); i++) {
				ps.setString(i + 1, params.get(i));
			}
			rs = ps.executeQuery();
			while (rs.next()) {
				recordCnt = rs.getInt("cnt");
			}
			
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			DatabaseUtils.closeAllResources(rs, ps, conn);
		}
		
		resultVO.setCurrentPage(pageNumber);
		resultVO.setRecordCnt(recordCnt);
		resultVO.setResult(list);
		return resultVO;
	}
	
	/**
	 * 创建新的DxCard学生记录
	 * @param dxCard 学生信息对象
	 * @return 是否创建成功
	 */
	public boolean createDxCard(DxCard dxCard) {
		Connection conn = null;
		PreparedStatement ps = null;
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "INSERT INTO dx_card (id, c_passwd, c_prov, c_city, c_name, c_university, c_major, create_tm, remark, c_phone, agent_id, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
			ps = conn.prepareStatement(SQL);
			
			ps.setString(1, UUID.randomUUID().toString());
			ps.setString(2, dxCard.getC_passwd());
			ps.setString(3, dxCard.getC_prov());
			ps.setString(4, dxCard.getC_city());
			ps.setString(5, dxCard.getC_name());
			ps.setString(6, dxCard.getC_university());
			ps.setString(7, dxCard.getC_major());
			ps.setTimestamp(8, new Timestamp(dxCard.getCreate_tm().getTime()));
			ps.setString(9, dxCard.getRemark());
			ps.setString(10, dxCard.getC_phone());
			ps.setString(11, dxCard.getAgent_id());
			ps.setInt(12, dxCard.getStatus());
			
			int affectedRows = ps.executeUpdate();
			
			SQLLogUtils.printSQL(" === createDxCard : ", ps);
			
			return affectedRows > 0;
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			DatabaseUtils.closeAllResources(null, ps, conn);
		}
		return false;
	}
	
	/**
	 * 拉取所有院校表数据
	 * @return
	 */
	public HashMap<String, ZyzdUniversityBean> getAllUniversities() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		HashMap<String, ZyzdUniversityBean> schoolMap = new HashMap<>();

		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_base_university";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			ZyzdUniversityBean bean = null;
			while (rs.next()) {
				bean = new ZyzdUniversityBean();

				bean.setYxmc(rs.getString("yxmc"));
				bean.setCnt_laboratory(rs.getString("cnt_laboratory"));
				bean.setCnt_landarea(rs.getString("cnt_landarea"));
				bean.setCnt_master(rs.getString("cnt_master"));
				bean.setCnt_phd(rs.getString("cnt_phd"));
				bean.setInd_catg(rs.getString("ind_catg"));
				bean.setInd_nature(rs.getString("ind_nature"));
				bean.setInd_type(rs.getString("ind_type"));
				bean.setInfo_addr(rs.getString("info_addr"));
				bean.setInfo_descp(rs.getString("info_descp"));
				bean.setInfo_found_time(rs.getString("info_found_time"));
				bean.setInfo_logo(rs.getString("info_logo"));
				bean.setInfo_tel(rs.getString("info_tel"));
				bean.setInfo_website(rs.getString("info_website"));
				bean.setIs211(rs.getString("is211"));
				bean.setIs985(rs.getString("is985"));
				bean.setIsqj(rs.getString("isqj"));
				bean.setIssyl(rs.getString("issyl"));
				bean.setLsy(rs.getString("lsy"));
				bean.setPosition_cs(rs.getString("position_cs"));
				bean.setPosition_qy(rs.getString("position_qy"));
				bean.setPosition_sf(rs.getString("position_sf"));
				bean.setRanking_qs(rs.getString("ranking_qs"));
				bean.setRanking_rk(rs.getString("ranking_rk"));
				bean.setRanking_usnews(rs.getString("ranking_usnews"));
				bean.setRanking_wsl(rs.getString("ranking_wsl"));
				bean.setRanking_xyh(rs.getString("ranking_xyh"));
				bean.setYxmc_used(rs.getString("yxmc_used"));
				bean.setCnt_grad(rs.getFloat("cnt_grad"));
				bean.setCnt_company(rs.getInt("cnt_company"));
				bean.setCnt_employ(rs.getInt("cnt_employ"));
				bean.setYx_tags(rs.getString("yx_tags"));
				bean.setYx_tags_all(rs.getString("yx_tags_all"));

				schoolMap.put(bean.getYxmc(), bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			DatabaseUtils.closeAllResources(rs, ps, conn);
		}
		return schoolMap;
	}
	
	public List<ZyzdBaseMajor> getZymlThree() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdBaseMajor> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "select * from zyzd_base_major x ORDER BY x.m_cc, x.m_catg_one, x.m_catg_two, x.sort desc";
			
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getZymlThree : ", ps);
			
			ZyzdBaseMajor bean = null;
			while (rs.next()) {
				bean = new ZyzdBaseMajor();
				bean.setM_cando(rs.getString("m_cando"));
				bean.setId(rs.getInt("id"));
				bean.setM_catg_one(rs.getString("m_catg_one"));
				bean.setM_catg_two(rs.getString("m_catg_two"));
				bean.setM_cc(rs.getString("m_cc"));
				bean.setM_cc_code(rs.getString("m_cc_code"));
				bean.setM_jy_area(rs.getString("m_jy_area"));
				bean.setM_jy_gw(rs.getString("m_jy_gw"));
				bean.setM_jy_hy(rs.getString("m_jy_hy"));
				bean.setM_jy_to(rs.getString("m_jy_to"));
				bean.setM_learn(rs.getString("m_learn"));
				bean.setM_ratio_jy(rs.getString("m_ratio_jy"));
				bean.setM_ratio_xb(rs.getString("m_ratio_xb"));
				bean.setM_sentense(rs.getString("m_sentense"));
				bean.setM_what(rs.getString("m_what"));
				bean.setM_xk(rs.getString("m_xk"));
				bean.setM_year_cnt(rs.getString("m_year_cnt"));
				bean.setM_zydm(rs.getString("m_zydm"));
				bean.setM_zymc(rs.getString("m_zymc"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			DatabaseUtils.closeAllResources(rs, ps, conn);
		}
		return list;
	}
	
	/**
	 * 
	 * 获取所有的院校专业Tag数据
	 * 
	 * @return
	 * 
	 */
	public List<ZyzdBaseUniversityAndMajorTag> getZyzdBaseUniversityAndMajorTagAll() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdBaseUniversityAndMajorTag> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_base_university_and_major_tag";
			
			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();
			
			SQLLogUtils.printSQL(" === getZymlThree : ", ps);
			
			ZyzdBaseUniversityAndMajorTag bean = null;
			while (rs.next()) {
				bean = new ZyzdBaseUniversityAndMajorTag();
	            bean.setNf(rs.getInt("nf"));
	            bean.setYxmc_org(rs.getString("yxmc_org"));
	            bean.setZymc_org(rs.getString("zymc_org"));
	            bean.setYxbq(rs.getString("yxbq"));
	            bean.setYxsp(rs.getString("yxsp"));
	            bean.setGmhbzs(rs.getString("gmhbzs"));
	            bean.setLsdw(rs.getString("lsdw"));
	            bean.setYxbyl(rs.getString("yxbyl"));
	            bean.setZzyqk(rs.getString("zzyqk"));
	            bean.setQxsszys(rs.getString("qxsszys"));
	            bean.setQxsszy(rs.getString("qxsszy"));
	            bean.setQxbszys(rs.getString("qxbszys"));
	            bean.setQxbszy(rs.getString("qxbszy"));
	            bean.setZysp(rs.getString("zysp"));
	            bean.setRkpm(rs.getString("rkpm"));
	            bean.setRkpj(rs.getString("rkpj"));
	            bean.setXkpg(rs.getString("xkpg"));
	            bean.setBzyssd(rs.getString("bzyssd"));
	            bean.setBzybsd(rs.getString("bzybsd"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			DatabaseUtils.closeAllResources(rs, ps, conn);
		}
		return list;
	}

	/**
	 * 获取所有研究生专业数据
	 * @return
	 */
	public List<ZyzdBaseMasterMajor> getAllMasterMajors() {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdBaseMasterMajor> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_base_master_major ORDER BY m_catg_one, m_catg_two, sort";

			ps = conn.prepareStatement(SQL);
			rs = ps.executeQuery();

			SQLLogUtils.printSQL(" === getAllMasterMajors : ", ps);

			ZyzdBaseMasterMajor bean = null;
			while (rs.next()) {
				bean = new ZyzdBaseMasterMajor();
				bean.setId(rs.getInt("id"));
				bean.setM_catg_one_code(rs.getString("m_catg_one_code"));
				bean.setM_catg_two_code(rs.getString("m_catg_two_code"));
				bean.setM_catg_one(rs.getString("m_catg_one"));
				bean.setM_catg_two(rs.getString("m_catg_two"));
				bean.setM_type(rs.getString("m_type"));
				bean.setM_zymc(rs.getString("m_zymc"));
				bean.setM_zydm(rs.getString("m_zydm"));
				bean.setSort(rs.getInt("sort"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			DatabaseUtils.closeAllResources(rs, ps, conn);
		}
		return list;
	}
	
	/**
	 * 根据专业名称，或则国考省考岗位
	 * @param year
	 * @param yrsj
	 * @param gzdd
	 * @return
	 */
	public List<CareerKG> getKgListByMajor(String zymc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<CareerKG> list = new ArrayList<>();
		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT nf, SF, bmmc, COUNT(*) as cnt FROM career_kg x WHERE x.zy LIKE ? GROUP BY nf, SF, x.bmmc ORDER BY COUNT(*) DESC ";
			Tools.println(SQL);
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + zymc + "%");
			rs = ps.executeQuery();
			CareerKG careerKG = null;
			while (rs.next()) {
				careerKG = new CareerKG();
				careerKG.setNf(String.valueOf(rs.getInt("nf")));
				careerKG.setSf(rs.getString("sf"));
				careerKG.setBmmc(rs.getString("bmmc"));
				careerKG.setCnt(rs.getInt("cnt"));
				list.add(careerKG);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			DatabaseUtils.closeAllResources(rs, ps, conn);
		}
		return list;
	}

	/**
	 * 根据院校名称查询特殊班信息
	 * @param yxmc 院校名称
	 * @return 特殊班信息列表
	 */
	public List<ZyzdBaseUniversityTsb> getTsbByYxmc(String yxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdBaseUniversityTsb> list = new ArrayList<>();

		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_base_university_tsb WHERE yxmc = ? ORDER BY bx";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmc);
			rs = ps.executeQuery();

			SQLLogUtils.printSQL("=== getTsbByYxmc : ", ps);

			while (rs.next()) {
				ZyzdBaseUniversityTsb bean = new ZyzdBaseUniversityTsb();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setBx(rs.getString("bx"));
				bean.setJs(rs.getString("js"));
				bean.setBz(rs.getString("bz"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			DatabaseUtils.closeAllResources(rs, ps, conn);
		}
		return list;
	}

	/**
	 * 根据院校名称查询转专业信息（按年份降序排列）
	 * @param yxmc 院校名称
	 * @return 转专业信息列表
	 */
	public List<ZyzdBaseUniversityZzy> getZzyByYxmc(String yxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdBaseUniversityZzy> list = new ArrayList<>();

		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_base_university_zzy WHERE yxmc = ? ORDER BY nf DESC";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, yxmc);
			rs = ps.executeQuery();

			SQLLogUtils.printSQL("=== getZzyByYxmc : ", ps);

			while (rs.next()) {
				ZyzdBaseUniversityZzy bean = new ZyzdBaseUniversityZzy();
				bean.setYxmc(rs.getString("yxmc"));
				bean.setNf(rs.getInt("nf"));
				bean.setYq(rs.getString("yq"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			DatabaseUtils.closeAllResources(rs, ps, conn);
		}
		return list;
	}

	/**
	 * 根据院校名称查询国家重点实验室信息
	 * @param yxmc 院校名称
	 * @return 国家重点实验室信息列表
	 */
	public List<ZyzdBaseUniversityLabs> getLabsByYxmc(String yxmc) {
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		List<ZyzdBaseUniversityLabs> list = new ArrayList<>();

		try {
			conn = DatabaseUtils.getConnection();
			String SQL = "SELECT * FROM zyzd_base_university_labs WHERE supporting_unit LIKE ? ORDER BY lab_name";
			ps = conn.prepareStatement(SQL);
			ps.setString(1, "%" + yxmc + "%");
			rs = ps.executeQuery();

			SQLLogUtils.printSQL("=== getLabsByYxmc : ", ps);

			while (rs.next()) {
				ZyzdBaseUniversityLabs bean = new ZyzdBaseUniversityLabs();
				bean.setId(rs.getInt("id"));
				bean.setLab_name(rs.getString("lab_name"));
				bean.setSupporting_unit(rs.getString("supporting_unit"));
				bean.setRegion(rs.getString("region"));
				bean.setYear_added(rs.getObject("year_added", Integer.class));
				bean.setSecondary_college(rs.getString("secondary_college"));
				list.add(bean);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			DatabaseUtils.closeAllResources(rs, ps, conn);
		}
		return list;
	}
	
	/**
	 * 根据学校和专业，查询出该院校+专业对应的给定年份的考研分数
	 * @param yxmc 院校名称
	 * @return 排序后的列表
	 */
	public List<CareerUniversityMajorData> getUniversityMajorDataByYxmcAndZymc(String nf, String yxmc, String zymc) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<CareerUniversityMajorData> list = new ArrayList<>();

	    try {
	        conn = DatabaseUtils.getConnection();

	        String sql = "SELECT * FROM career_university_major_data WHERE school_name LIKE ? and special_name = ? and data_year = ? ORDER BY total DESC";
	        ps = conn.prepareStatement(sql);
	        ps.setString(1, "%" + yxmc + "%");
	        ps.setString(2, zymc);
	        ps.setString(3, nf);
	        rs = ps.executeQuery();
	        
	        SQLLogUtils.printSQL("=== getUniversityMajorDataByYxmcAndZymc : ", ps);

	        while (rs.next()) {
	            CareerUniversityMajorData bean = new CareerUniversityMajorData();
	            bean.setSchool_id(rs.getInt("school_id"));
	            bean.setSchool_name(rs.getString("school_name"));
	            bean.setData_year(rs.getInt("data_year"));
	            bean.setDegree_type(rs.getInt("degree_type"));
	            bean.setDegree_type_name(rs.getString("degree_type_name"));
	            bean.setSpecial_code(rs.getString("special_code"));
	            bean.setSpecial_name(rs.getString("special_name"));
	            bean.setTotal(rs.getInt("total"));
	            bean.setPolitics(rs.getInt("politics"));
	            bean.setEnglish(rs.getInt("english"));
	            bean.setSpecial_one(rs.getInt("special_one"));
	            bean.setSpecial_two(rs.getInt("special_two"));
	            bean.setNote(rs.getString("note"));
	            bean.setDiff_total(rs.getInt("diff_total"));
	            bean.setDiff_politics(rs.getInt("diff_politics"));
	            bean.setDiff_english(rs.getInt("diff_english"));
	            bean.setDiff_special_one(rs.getInt("diff_special_one"));
	            bean.setDiff_special_two(rs.getInt("diff_special_two"));
	            list.add(bean);
	        }
	    } catch (Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        DatabaseUtils.closeAllResources(rs, ps, conn);
	    }

	    return list;
	}

	/**
	 * 根据学校和专业集合（多个专业），查询出该院校+专业集合 对应的给定年份的考研分数
	 * @param yxmc 院校名称
	 * @return 排序后的列表
	 */
	public List<CareerUniversityMajorData> getUniversityMajorDataByYxmcAndZymc(String nf, String yxmc, HashSet<String> zymcSets) {
	    Connection conn = null;
	    PreparedStatement ps = null;
	    ResultSet rs = null;
	    List<CareerUniversityMajorData> list = new ArrayList<>();

	    try {
	        conn = DatabaseUtils.getConnection();

	        String sql = "SELECT * FROM career_university_major_data WHERE school_name LIKE ? and special_name in ("+Tools.getSQLQueryin(zymcSets)+") and data_year = ? ORDER BY total DESC";
	        ps = conn.prepareStatement(sql);
	        ps.setString(1, "%" + yxmc + "%");
	        ps.setString(1, nf);
	        rs = ps.executeQuery();
	        
	        SQLLogUtils.printSQL("=== getUniversityMajorDataByYxmcAndZymc : ", ps);

	        while (rs.next()) {
	            CareerUniversityMajorData bean = new CareerUniversityMajorData();
	            bean.setSchool_id(rs.getInt("school_id"));
	            bean.setSchool_name(rs.getString("school_name"));
	            bean.setData_year(rs.getInt("data_year"));
	            bean.setDegree_type(rs.getInt("degree_type"));
	            bean.setDegree_type_name(rs.getString("degree_type_name"));
	            bean.setSpecial_code(rs.getString("special_code"));
	            bean.setSpecial_name(rs.getString("special_name"));
	            bean.setTotal(rs.getInt("total"));
	            bean.setPolitics(rs.getInt("politics"));
	            bean.setEnglish(rs.getInt("english"));
	            bean.setSpecial_one(rs.getInt("special_one"));
	            bean.setSpecial_two(rs.getInt("special_two"));
	            bean.setNote(rs.getString("note"));
	            bean.setDiff_total(rs.getInt("diff_total"));
	            bean.setDiff_politics(rs.getInt("diff_politics"));
	            bean.setDiff_english(rs.getInt("diff_english"));
	            bean.setDiff_special_one(rs.getInt("diff_special_one"));
	            bean.setDiff_special_two(rs.getInt("diff_special_two"));
	            list.add(bean);
	        }
	    } catch (Exception ex) {
	        ex.printStackTrace();
	    } finally {
	        DatabaseUtils.closeAllResources(rs, ps, conn);
	    }

	    return list;
	}

}
