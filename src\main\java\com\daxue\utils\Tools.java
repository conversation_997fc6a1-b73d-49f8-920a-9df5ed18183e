package com.daxue.utils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;

public class Tools {
	
	public static String trim(String str) {
		return (str == null) ? "" : str.trim();
	}
	
	public static boolean isEmpty(String str) {
		return (trim(str).length() == 0);
	}
	
	public static void println(String str) {
		System.out.println(str);  
	}
	
	public static String getDate(Date dt) {
		try {
		  DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
	      return df.format(dt);
	    } catch (Exception ex) {
	      return null;
	    } 
	}
	
	public static String getDateYMD(Date dt) {
		try {
		  DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
	      return df.format(dt);
	    } catch (Exception ex) {
	      return null;
	    } 
	}
		  
	public static int getInt(String str) {
		try {
			return Integer.parseInt(str.trim());
		} catch (Exception ex) {
			return -1;
		}
	}
	
	public static String view(String str) {
		return isEmpty(str) ? "-" : str.trim();
	}
	
	public static String viewForLimitLength(String str) {
		return viewForLimitLength(str, 7);
	}
	
	public static String viewForLimitLength(String str, int length) {
		if (isEmpty(str))
			return "-";
		str = str.trim();
		if (str.length() <= length)
			return str;
		return String.valueOf(str.substring(0, length)) + "*";
	}

	/**
     * 转义JavaScript字符串中的特殊字符，确保其在JS中安全使用。
     * 主要处理单引号、双引号、反斜杠、换行符和回车符。
     * @param input 需要转义的字符串
     * @return 转义后的字符串
     */
    public static String escapeJsQuotes(String input) {
        if (input == null) {
            return "";
        }
        return input.replace("\\", "\\\\") // 先转义反斜杠
                    .replace("'", "\\\'")  // 转义单引号
                    .replace("\"", "\\\"") // 转义双引号
                    .replace("\n", "\\n")  // 转义换行符
                    .replace("\r", "\\r"); // 转义回车符
    }

    /**
     * 显示数字值，当值为0时显示为"-"
     * @param value 数字值
     * @return 格式化后的字符串
     */
    public static String viewNumber(int value) {
        return value == 0 ? "-" : String.valueOf(value);
    }

    /**
     * HTML转义方法，防止XSS攻击
     * 将HTML特殊字符转换为HTML实体
     * @param input 需要转义的字符串
     * @return 转义后的安全字符串
     */
    public static String escapeHtml(String input) {
        if (input == null) return "";
        return input.replace("&", "&amp;")
                    .replace("<", "&lt;")
                    .replace(">", "&gt;")
                    .replace("\"", "&quot;")
                    .replace("'", "&#x27;");
    }

    /**
     * 安全显示方法，结合HTML转义和空值处理
     * @param input 需要显示的字符串
     * @return 安全的显示字符串，空值时返回"-"
     */
    public static String safeDisplay(String input) {
        if (isEmpty(input)) {
            return "-";
        }
        return escapeHtml(input.trim());
    }

    /**
     * 安全显示方法（允许空值显示为空字符串）
     * @param input 需要显示的字符串
     * @return 安全的显示字符串
     */
    public static String safeDisplayAllowEmpty(String input) {
        return escapeHtml(input);
    }
    
    public static String getSQLQueryin(HashSet<String> sets) {
		if (sets == null || sets.size() == 0)
			return "";
		String str = "";
		Iterator<String> it = sets.iterator();
		int index = 1;
		while (it.hasNext()) {
			String temp = it.next();
			if (!isEmpty(temp)) {
				str = String.valueOf(str) + "'" + temp + "'";
				if (index != sets.size())
					str = String.valueOf(str) + ",";
			}
			index++;
		}
		return str;
	}
}
